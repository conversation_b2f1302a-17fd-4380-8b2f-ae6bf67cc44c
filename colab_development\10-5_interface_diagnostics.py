# 10-5_interface_diagnostics.py
# 聊天界面诊断检查 - 在界面创建前进行全面检查

import os
import sys
from datetime import datetime

def check_chat_interface_modules():
    """检查聊天界面相关模块是否正确加载"""
    print("🔍 检查聊天界面模块...")
    
    # 检查基础模块
    required_modules = [
        'config', 'session_manager', 'rag_system', 'script_manager', 
        'model_manager', 'export_manager', 'RAGDocument'
    ]
    
    # 检查聊天界面模块
    chat_modules = [
        'chat_core', 'chat_session', 'chat_model', 'chat_ui'
    ]
    
    all_modules = required_modules + chat_modules
    available_modules = []
    missing_modules = []
    
    for module_name in all_modules:
        try:
            module_obj = globals().get(module_name)
            if module_obj is not None:
                available_modules.append(module_name)
                print(f"✅ {module_name}: 可用")
            else:
                missing_modules.append(module_name)
                print(f"❌ {module_name}: 缺失")
        except Exception as e:
            missing_modules.append(f"{module_name}({e})")
            print(f"❌ {module_name}: 错误 - {e}")
    
    print(f"\n📊 模块统计:")
    print(f"✅ 可用模块: {len(available_modules)}/{len(all_modules)}")
    print(f"❌ 缺失模块: {len(missing_modules)}")
    
    return available_modules, missing_modules

def check_chat_interface_object():
    """检查chat_interface对象的状态"""
    print("\n🎨 检查chat_interface对象...")
    
    try:
        # 检查chat_interface是否存在
        if 'chat_interface' not in globals():
            print("❌ chat_interface 未在全局作用域中定义")
            return False, "chat_interface未定义"
        
        chat_interface_obj = globals().get('chat_interface')
        if chat_interface_obj is None:
            print("❌ chat_interface 为 None")
            return False, "chat_interface为None"
        
        print("✅ chat_interface 对象存在")
        
        # 检查对象属性
        required_attrs = ['core', 'session', 'model', 'ui']
        missing_attrs = []
        
        for attr in required_attrs:
            if hasattr(chat_interface_obj, attr):
                attr_value = getattr(chat_interface_obj, attr)
                if attr_value is not None:
                    print(f"✅ chat_interface.{attr}: 可用")
                else:
                    print(f"❌ chat_interface.{attr}: None")
                    missing_attrs.append(attr)
            else:
                print(f"❌ chat_interface.{attr}: 缺失")
                missing_attrs.append(attr)
        
        # 检查方法
        if not hasattr(chat_interface_obj, 'create_interface'):
            print("❌ chat_interface 缺少 create_interface 方法")
            return False, "缺少create_interface方法"
        
        print("✅ create_interface 方法存在")
        
        if missing_attrs:
            return False, f"缺少属性: {', '.join(missing_attrs)}"
        
        return True, "chat_interface对象正常"
        
    except Exception as e:
        print(f"❌ 检查chat_interface对象时出错: {e}")
        return False, str(e)

def check_ui_component():
    """检查UI组件的状态"""
    print("\n🖼️ 检查UI组件...")
    
    try:
        if 'chat_ui' not in globals():
            print("❌ chat_ui 未定义")
            return False, "chat_ui未定义"
        
        chat_ui_obj = globals().get('chat_ui')
        if chat_ui_obj is None:
            print("❌ chat_ui 为 None")
            return False, "chat_ui为None"
        
        print("✅ chat_ui 对象存在")
        
        # 检查UI对象的方法
        if not hasattr(chat_ui_obj, 'create_interface'):
            print("❌ chat_ui 缺少 create_interface 方法")
            return False, "chat_ui缺少create_interface方法"
        
        print("✅ chat_ui.create_interface 方法存在")
        
        return True, "UI组件正常"
        
    except Exception as e:
        print(f"❌ 检查UI组件时出错: {e}")
        return False, str(e)

def test_interface_creation():
    """测试界面创建过程"""
    print("\n🧪 测试界面创建...")
    
    try:
        # 检查chat_interface
        if 'chat_interface' not in globals():
            return False, "chat_interface未定义"
        
        chat_interface_obj = globals().get('chat_interface')
        if chat_interface_obj is None:
            return False, "chat_interface为None"
        
        # 检查UI组件
        if not hasattr(chat_interface_obj, 'ui') or chat_interface_obj.ui is None:
            return False, "chat_interface.ui为None"
        
        # 尝试调用create_interface方法
        print("🔧 尝试调用create_interface...")
        interface = chat_interface_obj.create_interface()
        
        if interface is None:
            return False, "create_interface返回None"
        
        print("✅ 界面创建测试成功")
        return True, "界面创建正常"
        
    except Exception as e:
        print(f"❌ 界面创建测试失败: {e}")
        import traceback
        print(f"❌ 详细错误:\n{traceback.format_exc()}")
        return False, str(e)

def check_gradio_availability():
    """检查Gradio是否正常工作"""
    print("\n📦 检查Gradio...")
    
    try:
        import gradio as gr
        print("✅ Gradio导入成功")
        
        # 测试创建简单组件
        test_textbox = gr.Textbox(placeholder="测试")
        print("✅ Gradio组件创建成功")
        
        return True, "Gradio正常"
        
    except Exception as e:
        print(f"❌ Gradio检查失败: {e}")
        return False, str(e)

def run_diagnostics():
    """运行完整诊断"""
    print("🔧 聊天界面诊断检查")
    print("=" * 50)
    print(f"⏰ 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 检查Gradio
    gradio_ok, gradio_msg = check_gradio_availability()
    
    # 2. 检查模块
    available_modules, missing_modules = check_chat_interface_modules()
    
    # 3. 检查chat_interface对象
    interface_ok, interface_msg = check_chat_interface_object()
    
    # 4. 检查UI组件
    ui_ok, ui_msg = check_ui_component()
    
    # 5. 测试界面创建
    creation_ok, creation_msg = test_interface_creation()
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 诊断结果:")
    print(f"📦 Gradio: {'✅' if gradio_ok else '❌'} - {gradio_msg}")
    print(f"📚 模块: {len(available_modules)}/{len(available_modules) + len(missing_modules)} - {len(missing_modules)}个缺失")
    print(f"🎨 Interface对象: {'✅' if interface_ok else '❌'} - {interface_msg}")
    print(f"🖼️ UI组件: {'✅' if ui_ok else '❌'} - {ui_msg}")
    print(f"🧪 界面创建: {'✅' if creation_ok else '❌'} - {creation_msg}")
    
    # 建议
    print("\n💡 建议:")
    
    if not gradio_ok:
        print("1. 重新安装Gradio: !pip install gradio")
    
    if missing_modules:
        print("2. 重新执行缺失的模块:")
        module_files = {
            'config': '03_config.py',
            'session_manager': '06_session_manager.py',
            'rag_system': '08_rag_document_system.py',
            'script_manager': '05_json_script_manager.py',
            'model_manager': '09_model_manager.py',
            'export_manager': '07_export_manager.py',
            'RAGDocument': '04_data_models.py',
            'chat_core': '10-1_chat_interface_core.py',
            'chat_session': '10-2_chat_interface_session.py',
            'chat_model': '10-3_chat_interface_model.py',
            'chat_ui': '10-4_chat_interface_ui.py'
        }
        
        for module in missing_modules:
            if module in module_files:
                print(f"   - 重新执行: {module_files[module]}")
    
    if not interface_ok or not ui_ok:
        print("3. 重新执行聊天界面模块 (10-1 到 10-4)")
    
    if not creation_ok:
        print("4. 检查上述所有问题后，重新执行 11_main_app.py")
    
    # 返回总体状态
    all_ok = gradio_ok and len(missing_modules) == 0 and interface_ok and ui_ok and creation_ok
    
    if all_ok:
        print("\n🎉 所有检查通过！可以继续执行主应用。")
    else:
        print("\n⚠️  发现问题，请按照上述建议修复后再继续。")
    
    return all_ok

# 自动运行诊断
print("🔍 开始聊天界面诊断检查...")
diagnostic_result = run_diagnostics()

if diagnostic_result:
    print("✅ 诊断通过，准备创建主应用...")
else:
    print("❌ 诊断发现问题，请修复后再继续")
