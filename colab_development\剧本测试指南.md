# 剧本测试指南 - 加载 Ursus_Werebear_Awakens.json

## 📋 当前配置状态

系统已经配置为自动加载您的剧本文件：
- **剧本路径**: `/content/drive/MyDrive/AI_Game_Master/scripts/Ursus_Werebear_Awakens.json`
- **自动加载**: 在第5个cell执行时自动尝试加载

## 🔍 检查剧本加载状态

### 方法1：查看第5个cell的输出
执行第5个cell (`05_json_script_manager.py`) 后，查看输出信息：

**成功加载的标志**：
```
✅ 剧本加载成功，包含 XX 个AI回复
📜 JSON剧本管理器加载完成
📊 剧本信息: {'file_path': '...', 'total_responses': XX, ...}
```

**文件不存在的标志**：
```
⚠️  剧本文件不存在: /content/drive/MyDrive/AI_Game_Master/scripts/Ursus_Werebear_Awakens.json
📝 创建了示例剧本: ...
```

### 方法2：在Colab中手动检查文件
在新的cell中运行：
```python
import os
script_path = '/content/drive/MyDrive/AI_Game_Master/scripts/Ursus_Werebear_Awakens.json'
print(f"文件是否存在: {os.path.exists(script_path)}")

if os.path.exists(script_path):
    import json
    with open(script_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    assistant_count = len([msg for msg in data['conversation'] if msg['role'] == 'assistant'])
    print(f"剧本包含 {assistant_count} 个AI回复")
    print(f"总对话条数: {len(data['conversation'])}")
else:
    print("❌ 剧本文件不存在")
```

## 📁 确保文件正确上传

### 步骤1：检查Google Drive目录结构
确保您的Google Drive中有以下结构：
```
我的云端硬盘/
└── AI_Game_Master/
    └── scripts/
        └── Ursus_Werebear_Awakens.json
```

### 步骤2：上传剧本文件（如果需要）
如果文件不存在，请：

1. **在Colab中创建目录**：
```python
import os
os.makedirs('/content/drive/MyDrive/AI_Game_Master/scripts', exist_ok=True)
```

2. **上传文件方法A - 直接上传**：
```python
from google.colab import files
uploaded = files.upload()
# 选择您的 Ursus_Werebear_Awakens.json 文件

# 移动到正确位置
import shutil
for filename in uploaded.keys():
    if filename.endswith('.json'):
        shutil.move(filename, '/content/drive/MyDrive/AI_Game_Master/scripts/Ursus_Werebear_Awakens.json')
        print(f"✅ 文件已上传到: /content/drive/MyDrive/AI_Game_Master/scripts/Ursus_Werebear_Awakens.json")
```

3. **上传文件方法B - 通过Google Drive界面**：
   - 在浏览器中打开Google Drive
   - 导航到 `AI_Game_Master/scripts/` 目录
   - 直接拖拽上传 `Ursus_Werebear_Awakens.json` 文件

## 🎮 测试剧本功能

### 重新加载剧本
如果您上传了新的剧本文件，需要重新加载：
```python
# 重新加载剧本
script_manager.load_script()
print("📊 剧本信息:", script_manager.get_script_info())
```

### 测试剧本回复
```python
# 测试获取回复
test_response = script_manager.get_response("测试输入")
print("🎭 剧本回复:", test_response)

# 重置对话索引
script_manager.reset_conversation()
```

### 验证剧本内容
```python
# 查看剧本的前几个回复
for i, response in enumerate(script_manager.assistant_responses[:3]):
    print(f"回复 {i+1}: {response[:100]}...")
```

## 🔧 故障排除

### 问题1：文件路径错误
**症状**: 显示文件不存在，但您确定已上传
**解决**: 检查文件名是否完全匹配（区分大小写）

### 问题2：JSON格式错误
**症状**: 加载失败，显示JSON解析错误
**解决**: 
```python
import json
try:
    with open('/content/drive/MyDrive/AI_Game_Master/scripts/Ursus_Werebear_Awakens.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    print("✅ JSON格式正确")
except json.JSONDecodeError as e:
    print(f"❌ JSON格式错误: {e}")
```

### 问题3：编码问题
**症状**: 加载时出现编码错误
**解决**: 确保文件以UTF-8编码保存

## 📊 预期的剧本统计

根据您的 `Ursus_Werebear_Awakens.json` 文件，预期应该看到：
- 大量的AI回复（assistant角色的消息）
- 丰富的Elder Scrolls世界观内容
- 复杂的RPG对话和选择

## ✅ 成功标志

当一切正常时，您应该看到：
1. ✅ 剧本加载成功的消息
2. 📊 正确的回复数量统计
3. 🎮 在聊天界面中能够获得来自剧本的回复

## 🎯 开始测试

一旦剧本加载成功，您就可以：
1. 在聊天界面中输入各种指令
2. 观察AI回复是否来自您的剧本
3. 测试RAG文档系统是否正确提取游戏信息
4. 验证导出功能是否正常工作

---

**提示**: 如果遇到任何问题，请先检查第5个cell的输出信息，那里会显示详细的加载状态。
