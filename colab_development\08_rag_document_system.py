# 08_rag_document_system.py
# RAG文档系统 - 自动管理游戏状态文档

import os
import json
import re
from datetime import datetime
from typing import List, Dict, Any, Optional

class RAGDocumentSystem:
    """RAG文档系统 - 自动维护游戏状态文档"""
    
    def __init__(self, rag_docs_path: str):
        self.rag_docs_path = rag_docs_path
        self.rag_document = RAGDocument()
        self.update_counter = 0
        
        # 确保目录存在
        os.makedirs(rag_docs_path, exist_ok=True)
        
        # 英文关键词模式
        self.item_patterns = [
            r'You (?:gain|get|find|obtain|receive|acquire|loot) (?:a |an |the )?([^.!?\n]+?)(?:\s*x(\d+))?[.!?\n]',
            r'(?:Gained|Found|Obtained|Received|Acquired|Looted):?\s*([^.!?\n]+?)(?:\s*x(\d+))?[.!?\n]',
            r'(?:You now have|You possess|Added to inventory):?\s*([^.!?\n]+?)(?:\s*x(\d+))?[.!?\n]',
            r'\+\s*(\d+)\s+([^.!?\n]+?)(?:\s+added?)?[.!?\n]'
        ]

        self.event_patterns = [
            r'You (?:defeat|kill|slay|destroy|overcome) (?:a |an |the )?([^.!?\n]+)[.!?\n]',
            r'You (?:enter|arrive at|reach|discover) (?:a |an |the )?([^.!?\n]+)[.!?\n]',
            r'You (?:complete|finish|accomplish) (?:a |an |the )?([^.!?\n]+)[.!?\n]',
            r'You (?:learn|master|unlock) (?:a |an |the )?([^.!?\n]+)[.!?\n]',
            r'(?:Quest completed|Mission accomplished|Achievement unlocked):?\s*([^.!?\n]+)[.!?\n]'
        ]

        self.choice_patterns = [
            r'I (?:choose|select|pick|decide) (?:to )?(.+)',
            r'I (?:want to|will|am going to) (.+)',
            r'My choice is (.+)',
            r'\{(\d+)\}.*?(.+?)(?:\}|$)'  # 选择格式 {1} {action}
        ]
    
    def analyze_message(self, role: str, content: str):
        """分析消息并更新RAG文档"""
        try:
            if role == 'user':
                self._analyze_user_message(content)
            elif role == 'assistant':
                self._analyze_assistant_message(content)

            self.update_counter += 1

            # 定期保存
            if self.update_counter >= config.rag_config['update_frequency']:
                self.save_rag_documents()
                self.update_counter = 0

        except Exception as e:
            print(f"⚠️  RAG分析出错，跳过此消息: {e}")
            # 不阻断进程，继续执行
    
    def _analyze_user_message(self, content: str):
        """分析用户消息"""
        try:
            # 提取玩家选择
            for pattern in self.choice_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for match in matches:
                    choice = match.strip()
                    if choice and len(choice) < 100:  # 避免过长的选择
                        self.rag_document.player_preferences['choices'].append(choice)
                        # 保持最近的选择
                        if len(self.rag_document.player_preferences['choices']) > 20:
                            self.rag_document.player_preferences['choices'] = \
                                self.rag_document.player_preferences['choices'][-20:]
        except Exception as e:
            print(f"⚠️  用户消息分析出错: {e}")
    
    def _analyze_assistant_message(self, content: str):
        """分析AI回复消息"""
        try:
            # 提取物品信息
            self._extract_items(content)
        except Exception as e:
            print(f"⚠️  物品提取出错: {e}")

        try:
            # 提取事件信息
            self._extract_events(content)
        except Exception as e:
            print(f"⚠️  事件提取出错: {e}")

        try:
            # 分析游戏状态
            self._analyze_game_state(content)
        except Exception as e:
            print(f"⚠️  游戏状态分析出错: {e}")
    
    def _extract_items(self, content: str):
        """从内容中提取物品信息"""
        for pattern in self.item_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                try:
                    if isinstance(match, tuple):
                        item_name = match[0].strip()
                        quantity_str = match[1] if match[1] else "1"

                        # 验证数量字符串是否为有效数字
                        if self._is_valid_quantity(quantity_str):
                            quantity = int(quantity_str)
                        else:
                            print(f"⚠️  无效的物品数量: '{quantity_str}', 使用默认值1")
                            quantity = 1
                    else:
                        item_name = match.strip()
                        quantity = 1

                    # 清理物品名称
                    item_name = self._clean_item_name(item_name)

                    # 验证物品名称的有效性
                    if self._is_valid_item_name(item_name):
                        self.rag_document.add_item(item_name, quantity)
                        print(f"📦 检测到物品: {item_name} x{quantity}")

                except Exception as e:
                    print(f"⚠️  物品提取错误: {e} - 跳过此项")
    
    def _clean_item_name(self, item_name: str) -> str:
        """清理物品名称"""
        # 移除常见的英文无关词汇
        stop_words = ['a', 'an', 'the', 'some', 'of', 'from', 'to', 'in', 'on', 'at']
        words = item_name.split()
        cleaned_words = [word for word in words if word.lower() not in stop_words]

        # 重新组合
        item_name = ' '.join(cleaned_words)

        # 移除多余的标点符号，但保留连字符和撇号
        item_name = re.sub(r'[^\w\s\-\']', '', item_name)

        return item_name.strip()

    def _is_valid_quantity(self, quantity_str: str) -> bool:
        """验证数量字符串是否为有效数字"""
        if not quantity_str:
            return False

        # 移除空格
        quantity_str = quantity_str.strip()

        # 检查是否只包含数字
        if not quantity_str.isdigit():
            return False

        # 检查数字范围（避免过大的数字）
        try:
            num = int(quantity_str)
            return 1 <= num <= 9999
        except ValueError:
            return False

    def _is_valid_item_name(self, item_name: str) -> bool:
        """验证物品名称是否有效"""
        if not item_name or len(item_name) < 2:
            return False

        # 排除明显的战斗描述
        combat_keywords = ['damage', 'hit', 'miss', 'roll', 'attack', 'defense', 'hp', 'health']
        item_lower = item_name.lower()

        for keyword in combat_keywords:
            if keyword in item_lower:
                return False

        # 排除包含数学符号的内容
        if any(char in item_name for char in ['=', '+', '-', '*', '/', '(', ')', 'd20', 'd12', 'd6']):
            return False

        # 长度限制
        if len(item_name) > 50:
            return False

        return True
    
    def _extract_events(self, content: str):
        """从内容中提取事件信息"""
        try:
            for pattern in self.event_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for match in matches:
                    try:
                        event = match.strip()
                        if event and len(event) < 200:
                            # 判断重要性
                            importance = self._determine_importance(event)

                            # 提取游戏时间（如果有）
                            game_time = self._extract_game_time(content)

                            self.rag_document.add_event(event, importance, game_time)
                            print(f"📋 检测到事件: {event} ({importance})")
                    except Exception as e:
                        print(f"⚠️  单个事件处理出错: {e}")
        except Exception as e:
            print(f"⚠️  事件提取整体出错: {e}")
    
    def _determine_importance(self, event: str) -> str:
        """判断事件重要性"""
        high_keywords = ['defeat', 'kill', 'slay', 'destroy', 'death', 'die', 'complete', 'finish',
                        'accomplish', 'level up', 'skill', 'treasure', 'artifact', 'boss', 'dragon']
        medium_keywords = ['enter', 'arrive', 'reach', 'discover', 'find', 'meet', 'encounter',
                          'learn', 'unlock', 'quest', 'mission']

        event_lower = event.lower()

        for keyword in high_keywords:
            if keyword in event_lower:
                return 'high'

        for keyword in medium_keywords:
            if keyword in event_lower:
                return 'medium'

        return 'low'
    
    def _extract_game_time(self, content: str) -> str:
        """提取游戏时间信息"""
        time_patterns = [
            r'(上午|下午|晚上|深夜)',
            r'(第\d+天)',
            r'(\d+点)',
            r'(黎明|正午|黄昏|午夜)'
        ]
        
        for pattern in time_patterns:
            match = re.search(pattern, content)
            if match:
                return match.group(1)
        
        return ""
    
    def _analyze_game_state(self, content: str):
        """分析游戏状态并更新玩家偏好"""
        try:
            content_lower = content.lower()

            # 分析战斗风格
            try:
                combat_keywords = ['attack', 'fight', 'battle', 'combat', 'weapon', 'sword', 'axe', 'bow']
                if any(word in content_lower for word in combat_keywords):
                    if any(word in content_lower for word in ['spell', 'magic', 'cast', 'enchant', 'mage']):
                        style = 'Magic Combat'
                    elif any(word in content_lower for word in ['sneak', 'stealth', 'assassin', 'backstab']):
                        style = 'Stealth Combat'
                    elif any(word in content_lower for word in ['bow', 'arrow', 'shoot', 'archer']):
                        style = 'Ranged Combat'
                    else:
                        style = 'Melee Combat'

                    current_style = self.rag_document.player_preferences.get('playstyle', '')
                    if style not in current_style:
                        new_style = f"{current_style}, {style}" if current_style else style
                        self.rag_document.update_preferences(playstyle=new_style)
            except Exception as e:
                print(f"⚠️  战斗风格分析出错: {e}")

            # 分析性格特征
            try:
                if any(word in content_lower for word in ['brave', 'courage', 'adventure', 'explore', 'bold']):
                    personality = self.rag_document.player_preferences.get('personality', '')
                    if 'brave' not in personality.lower():
                        new_personality = f"{personality}, Brave Adventurer" if personality else "Brave Adventurer"
                        self.rag_document.update_preferences(personality=new_personality)
            except Exception as e:
                print(f"⚠️  性格特征分析出错: {e}")

            # 分析道德倾向
            try:
                if any(word in content_lower for word in ['help', 'save', 'protect', 'heal']):
                    personality = self.rag_document.player_preferences.get('personality', '')
                    if 'heroic' not in personality.lower():
                        new_personality = f"{personality}, Heroic" if personality else "Heroic"
                        self.rag_document.update_preferences(personality=new_personality)
            except Exception as e:
                print(f"⚠️  道德倾向分析出错: {e}")

        except Exception as e:
            print(f"⚠️  游戏状态分析整体出错: {e}")
    
    def save_rag_documents(self) -> str:
        """保存RAG文档"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"rag_documents_{timestamp}.json"
        filepath = os.path.join(self.rag_docs_path, filename)
        
        try:
            data = self.rag_document.to_dict()
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"📋 RAG文档已保存: {filename}")
            return filepath
            
        except Exception as e:
            print(f"❌ 保存RAG文档失败: {e}")
            return ""
    
    def load_rag_documents(self, filepath: str) -> bool:
        """加载RAG文档"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 重建RAG文档
            self.rag_document = RAGDocument()
            
            # 加载物品
            if 'inventory' in data and 'items' in data['inventory']:
                for item_data in data['inventory']['items']:
                    self.rag_document.add_item(
                        item_data['name'],
                        item_data['quantity'],
                        item_data.get('description', '')
                    )
            
            # 加载事件
            if 'key_events' in data:
                for event_data in data['key_events']:
                    self.rag_document.add_event(
                        event_data['event'],
                        event_data.get('importance', 'medium'),
                        event_data.get('game_time', '')
                    )
            
            # 加载玩家偏好
            if 'player_preferences' in data:
                prefs = data['player_preferences']
                self.rag_document.update_preferences(
                    playstyle=prefs.get('playstyle', ''),
                    choices=prefs.get('choices', []),
                    personality=prefs.get('personality', '')
                )
            
            print(f"📂 RAG文档已加载: {os.path.basename(filepath)}")
            return True
            
        except Exception as e:
            print(f"❌ 加载RAG文档失败: {e}")
            return False
    
    def get_context_summary(self) -> str:
        """获取上下文摘要（用于AI参考）"""
        summary_parts = []
        
        # 物品摘要
        if self.rag_document.inventory:
            items = [f"{item.name}x{item.quantity}" for item in self.rag_document.inventory[-5:]]
            summary_parts.append(f"当前物品: {', '.join(items)}")
        
        # 重要事件摘要
        important_events = [event for event in self.rag_document.key_events if event.importance == 'high']
        if important_events:
            recent_events = [event.event for event in important_events[-3:]]
            summary_parts.append(f"重要事件: {'; '.join(recent_events)}")
        
        # 玩家偏好摘要
        prefs = self.rag_document.player_preferences
        if prefs['playstyle']:
            summary_parts.append(f"游戏风格: {prefs['playstyle']}")
        
        return "\n".join(summary_parts) if summary_parts else "暂无游戏状态记录"
    
    def get_stats(self) -> Dict[str, Any]:
        """获取RAG文档统计信息"""
        return {
            'total_items': len(self.rag_document.inventory),
            'total_events': len(self.rag_document.key_events),
            'high_importance_events': len([e for e in self.rag_document.key_events if e.importance == 'high']),
            'player_choices': len(self.rag_document.player_preferences['choices']),
            'last_updated': self.rag_document.last_updated.isoformat(),
            'update_counter': self.update_counter
        }

# 创建全局RAG文档系统实例
rag_system = RAGDocumentSystem(config.rag_docs_path)

print("📋 RAG文档系统加载完成")
print(f"📂 RAG文档路径: {config.rag_docs_path}")
print(f"📊 统计信息: {rag_system.get_stats()}")
print("🎮 准备加载聊天界面...")
