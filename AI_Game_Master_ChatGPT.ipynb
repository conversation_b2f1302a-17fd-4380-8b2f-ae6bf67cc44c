# 📦 安装依赖包
import subprocess
import sys

def install_package(package):
    subprocess.check_call([sys.executable, "-m", "pip", "install", package])

# 安装必要的包
packages = [
    "torch",
    "transformers>=4.36.0",
    "accelerate",
    "bitsandbytes",
    "gradio>=4.0.0",
    "python-docx",
    "sentence-transformers",
    "faiss-cpu",
    "numpy",
    "scikit-learn"
]

print("🔄 安装依赖包...")
for package in packages:
    try:
        install_package(package)
        print(f"✅ {package} 安装成功")
    except Exception as e:
        print(f"❌ {package} 安装失败: {e}")

print("\n🎉 依赖包安装完成！")

# 📚 导入库和初始化
import torch
import json
import datetime
import uuid
import os
import shutil
import gradio as gr
from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
from docx import Document
from docx.shared import Inches
import gc
from typing import List, Dict, Optional
import numpy as np
from sentence_transformers import SentenceTransformer
import faiss
from sklearn.feature_extraction.text import TfidfVectorizer
import re

print("✅ 库导入成功！")

# 🔧 配置类
class Config:
    """系统配置"""
    
    # 模型配置
    MODEL_OPTIONS = {
        "3b": {
            "name": "microsoft/DialoGPT-medium",
            "description": "3B参数模型 - 开发测试用",
            "quantization": True
        },
        "70b": {
            "name": "meta-llama/Llama-2-70b-chat-hf",
            "description": "70B参数模型 - 生产环境",
            "quantization": True
        }
    }
    
    # 当前使用的模型
    CURRENT_MODEL = "3b"  # 开发阶段使用3B模型
    
    # 游戏配置
    GAME_CONFIG = {
        "max_context_length": 4096,
        "max_new_tokens": 512,
        "temperature": 0.8,
        "top_p": 0.9,
        "repetition_penalty": 1.1
    }
    
    # 系统提示词
    SYSTEM_PROMPT = """
你是一位专业的Elder Scrolls世界AI游戏主持人。你的任务是为玩家创造沉浸式的角色扮演体验。

核心原则：
1. 保持Elder Scrolls世界观的一致性
2. 创造引人入胜的故事情节
3. 给予玩家有意义的选择
4. 描述生动的场景和角色
5. 保持游戏的平衡性和挑战性

回应格式：
- 使用生动的描述性语言
- 提供多个行动选项
- 保持神秘感和史诗感
- 适当使用Elder Scrolls术语
"""

print("✅ 配置初始化完成！")

# 🤖 AI模型管理器
class ModelManager:
    """AI模型加载和管理"""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.model_name = None
        print(f"🔧 模型管理器初始化完成，设备: {self.device}")
    
    def load_model(self, model_key: str) -> bool:
        """加载指定的AI模型"""
        if model_key not in Config.MODEL_OPTIONS:
            print(f"❌ 未知的模型配置: {model_key}")
            return False
        
        model_config = Config.MODEL_OPTIONS[model_key]
        model_name = model_config["name"]
        
        try:
            print(f"🔄 开始加载模型: {model_name}")
            
            # 清理之前的模型
            if self.model is not None:
                del self.model
                del self.tokenizer
                gc.collect()
                torch.cuda.empty_cache()
            
            # 量化配置
            quantization_config = None
            if model_config.get("quantization", False):
                quantization_config = BitsAndBytesConfig(
                    load_in_4bit=True,
                    bnb_4bit_compute_dtype=torch.float16,
                    bnb_4bit_use_double_quant=True,
                    bnb_4bit_quant_type="nf4"
                )
            
            # 加载tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                model_name,
                trust_remote_code=True
            )
            
            # 设置pad_token
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # 加载模型
            self.model = AutoModelForCausalLM.from_pretrained(
                model_name,
                quantization_config=quantization_config,
                device_map="auto",
                trust_remote_code=True,
                torch_dtype=torch.float16
            )
            
            self.model_name = model_name
            print(f"✅ 模型加载成功！设备: {self.device}")
            if torch.cuda.is_available():
                print(f"💾 GPU内存使用: {torch.cuda.memory_allocated()/1024**3:.2f}GB")
            return True
            
        except Exception as e:
            print(f"❌ 模型加载失败: {str(e)}")
            return False
    
    def generate_response(self, messages: List[Dict], **kwargs) -> str:
        """生成AI回应"""
        if not self.model or not self.tokenizer:
            return "❌ 模型未加载，请先运行模型加载单元格！"
        
        try:
            # 构建输入文本
            input_text = ""
            for msg in messages:
                role = "Human" if msg["role"] == "user" else "Assistant"
                input_text += f"{role}: {msg['content']}\n"
            input_text += "Assistant:"
            
            # 编码输入
            inputs = self.tokenizer.encode(input_text, return_tensors="pt").to(self.device)
            
            # 生成回应
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_new_tokens=kwargs.get("max_new_tokens", Config.GAME_CONFIG["max_new_tokens"]),
                    temperature=kwargs.get("temperature", Config.GAME_CONFIG["temperature"]),
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id
                )
            
            # 解码回应
            response = self.tokenizer.decode(outputs[0][inputs.shape[-1]:], skip_special_tokens=True)
            return response.strip()
            
        except Exception as e:
            return f"❌ 生成回应时出错: {str(e)}"

# 创建全局模型管理器
model_manager = ModelManager()
print("✅ 模型管理器创建完成！")

# 💾 会话管理器
class SessionManager:
    """游戏会话管理"""
    
    def __init__(self):
        self.current_session = {
            "session_id": str(uuid.uuid4()),
            "created_at": datetime.datetime.now().isoformat(),
            "conversation": [],
            "metadata": {
                "game_world": "Elder Scrolls",
                "version": "1.0"
            }
        }
        
        # 添加系统提示词
        self.add_message("system", Config.SYSTEM_PROMPT)
        print("✅ 会话管理器初始化完成！")
    
    def add_message(self, role: str, content: str):
        """添加消息到会话"""
        message = {
            "role": role,
            "content": content,
            "timestamp": datetime.datetime.now().isoformat()
        }
        self.current_session["conversation"].append(message)
    
    def get_messages_for_model(self, max_history: int = 10) -> List[Dict]:
        """获取用于模型的消息格式"""
        messages = []
        conversation = self.current_session["conversation"]
        
        # 总是包含系统提示词
        system_msg = next((msg for msg in conversation if msg["role"] == "system"), None)
        if system_msg:
            messages.append({"role": "system", "content": system_msg["content"]})
        
        # 获取最近的对话历史
        recent_messages = [msg for msg in conversation if msg["role"] in ["user", "assistant"]][-max_history:]
        
        for msg in recent_messages:
            messages.append({"role": msg["role"], "content": msg["content"]})
        
        return messages
    
    def get_conversation_summary(self) -> Dict:
        """获取会话摘要"""
        conversation = self.current_session["conversation"]
        
        return {
            "session_id": self.current_session["session_id"],
            "total_messages": len(conversation),
            "user_messages": len([msg for msg in conversation if msg["role"] == "user"]),
            "assistant_messages": len([msg for msg in conversation if msg["role"] == "assistant"]),
            "created_at": self.current_session["created_at"]
        }
    
    def save_session(self, filepath: str = None) -> str:
        """保存会话到JSON文件"""
        if filepath is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filepath = f"game_session_{timestamp}.json"
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.current_session, f, ensure_ascii=False, indent=2)
            print(f"✅ 会话已保存到: {filepath}")
            return filepath
        except Exception as e:
            print(f"❌ 保存会话失败: {str(e)}")
            return ""
    
    def export_to_docx(self, filepath: str = None) -> str:
        """导出会话到DOCX文档"""
        if filepath is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filepath = f"game_session_{timestamp}.docx"
        
        try:
            doc = Document()
            doc.add_heading('Elder Scrolls AI冒险记录', 0)
            
            # 添加会话信息
            summary = self.get_conversation_summary()
            doc.add_heading('会话信息', level=1)
            doc.add_paragraph(f"会话ID: {summary['session_id']}")
            doc.add_paragraph(f"创建时间: {summary['created_at']}")
            doc.add_paragraph(f"总消息数: {summary['total_messages']}")
            
            # 添加对话内容
            doc.add_heading('对话记录', level=1)
            
            for msg in self.current_session["conversation"]:
                if msg["role"] == "system":
                    continue
                
                role_name = "🎮 玩家" if msg["role"] == "user" else "🎭 游戏主持人"
                timestamp = msg.get("timestamp", "")
                
                p = doc.add_paragraph()
                p.add_run(f"{role_name} ({timestamp})\n").bold = True
                p.add_run(msg["content"])
                doc.add_paragraph()  # 空行
            
            doc.save(filepath)
            print(f"✅ DOCX已导出到: {filepath}")
            return filepath
        except Exception as e:
            print(f"❌ 导出DOCX失败: {str(e)}")
            return ""

# 创建全局会话管理器
session_manager = SessionManager()
print("✅ 会话管理器创建完成！")

# 📚 RAG文档管理器
class RAGManager:
    """RAG文档和知识管理"""
    
    def __init__(self):
        self.documents = {
            "inventory": [],
            "locations": [],
            "characters": [],
            "events": [],
            "choices": [],
            "lore": []
        }
        
        self.game_state = {
            "player_name": "",
            "current_location": "",
            "gold": 0,
            "level": 1,
            "health": 100,
            "skills": {},
            "reputation": {}
        }
        
        # 初始化向量化工具
        try:
            self.vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
            self.vectors = None
            print("✅ RAG向量化工具初始化完成")
        except Exception as e:
            print(f"⚠️ RAG向量化工具初始化失败: {e}")
            self.vectorizer = None
        
        print("✅ RAG管理器初始化完成！")
    
    def analyze_message_for_updates(self, role: str, content: str):
        """分析消息并更新游戏状态"""
        content_lower = content.lower()
        
        # 检测物品获得
        item_patterns = [
            r'获得了?\s*([^，。！？\n]+)',
            r'得到了?\s*([^，。！？\n]+)',
            r'拿到了?\s*([^，。！？\n]+)',
            r'收到了?\s*([^，。！？\n]+)'
        ]
        
        for pattern in item_patterns:
            matches = re.findall(pattern, content)
            for item in matches:
                item = item.strip()
                if item and item not in self.documents["inventory"]:
                    self.documents["inventory"].append(item)
        
        # 检测金币变化
        gold_patterns = [
            r'(\d+)\s*金币',
            r'(\d+)\s*gold',
            r'金币.*?(\d+)'
        ]
        
        for pattern in gold_patterns:
            matches = re.findall(pattern, content_lower)
            for match in matches:
                try:
                    gold_amount = int(match)
                    if '获得' in content or '得到' in content:
                        self.game_state["gold"] += gold_amount
                    elif '失去' in content or '花费' in content:
                        self.game_state["gold"] = max(0, self.game_state["gold"] - gold_amount)
                except ValueError:
                    continue
        
        # 检测地点
        location_keywords = ['到达', '来到', '进入', '离开', '前往']
        for keyword in location_keywords:
            if keyword in content:
                # 简单的地点提取逻辑
                parts = content.split(keyword)
                if len(parts) > 1:
                    potential_location = parts[1].split('，')[0].split('。')[0].strip()
                    if potential_location and len(potential_location) < 50:
                        if potential_location not in self.documents["locations"]:
                            self.documents["locations"].append(potential_location)
                        self.game_state["current_location"] = potential_location
        
        # 记录重要事件
        if role == "assistant" and len(content) > 100:
            event_summary = content[:200] + "..." if len(content) > 200 else content
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M")
            event = f"[{timestamp}] {event_summary}"
            self.documents["events"].append(event)
            
            # 保持事件列表不超过50条
            if len(self.documents["events"]) > 50:
                self.documents["events"] = self.documents["events"][-50:]
        
        # 记录玩家选择
        if role == "user":
            choice_summary = content[:100] + "..." if len(content) > 100 else content
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M")
            choice = f"[{timestamp}] {choice_summary}"
            self.documents["choices"].append(choice)
            
            # 保持选择列表不超过30条
            if len(self.documents["choices"]) > 30:
                self.documents["choices"] = self.documents["choices"][-30:]
    
    def get_context_for_ai(self) -> str:
        """获取用于AI的上下文信息"""
        context_parts = []
        
        # 游戏状态
        if self.game_state["current_location"]:
            context_parts.append(f"当前位置: {self.game_state['current_location']}")
        
        if self.game_state["gold"] > 0:
            context_parts.append(f"金币: {self.game_state['gold']}")
        
        # 库存物品
        if self.documents["inventory"]:
            items = ", ".join(self.documents["inventory"][-10:])  # 最近10个物品
            context_parts.append(f"库存物品: {items}")
        
        # 最近事件
        if self.documents["events"]:
            recent_events = self.documents["events"][-3:]  # 最近3个事件
            context_parts.append("最近事件:")
            for event in recent_events:
                context_parts.append(f"- {event}")
        
        return "\n".join(context_parts) if context_parts else ""
    
    def get_summary(self) -> Dict:
        """获取RAG文档摘要"""
        return {
            "inventory_items": len(self.documents["inventory"]),
            "locations_visited": len(self.documents["locations"]),
            "characters_met": len(self.documents["characters"]),
            "key_events": len(self.documents["events"]),
            "player_choices": len(self.documents["choices"]),
            "gold": self.game_state["gold"],
            "current_location": self.game_state["current_location"]
        }

# 创建全局RAG管理器
rag_manager = RAGManager()
print("✅ RAG管理器创建完成！")

# 🔄 加载AI模型
print("🔄 开始加载AI模型...")
print(f"📍 当前配置: {Config.MODEL_OPTIONS[Config.CURRENT_MODEL]['description']}")

# 挂载Google Drive（如果在Colab中）
try:
    from google.colab import drive
    drive.mount('/content/drive')
    print("✅ Google Drive已挂载")
except ImportError:
    print("ℹ️ 非Colab环境，跳过Drive挂载")

# 加载模型
success = model_manager.load_model(Config.CURRENT_MODEL)

if success:
    print("\n🎉 模型加载成功！")
    print("📊 系统状态:")
    print(f"  - 模型: {model_manager.model_name}")
    print(f"  - 设备: {model_manager.device}")
    if torch.cuda.is_available():
        print(f"  - GPU内存: {torch.cuda.memory_allocated()/1024**3:.2f}GB / {torch.cuda.memory_reserved()/1024**3:.2f}GB")
    
    print("\n🎮 AI游戏主持人已准备就绪！")
    print("🌍 欢迎来到Elder Scrolls世界！")
    
    # 生成开场白
    opening_messages = session_manager.get_messages_for_model(max_history=0)
    opening_messages.append({
        "role": "user", 
        "content": "请为玩家创建一个Elder Scrolls世界的开场场景，描述环境和初始情况。请保持神话感和史诗感，让玩家感受到这个世界的魅力。"
    })
    
    opening_response = model_manager.generate_response(opening_messages)
    session_manager.add_message("assistant", opening_response)
    print(f"\n🎭 开场白: {opening_response}")
    
    print("\n✨ 游戏已开始！请运行下方的聊天界面。")
else:
    print("\n❌ 模型加载失败！请检查配置和网络连接。")

# 🎮 ChatGPT风格聊天界面
def create_chatgpt_interface():
    """创建真正的ChatGPT风格界面"""
    
    def respond(message, history):
        """处理用户消息并返回AI回应"""
        if not message.strip():
            return "", history
        
        try:
            # 添加用户消息到会话
            session_manager.add_message("user", message)
            rag_manager.analyze_message_for_updates("user", message)
            
            # 获取AI回应
            messages = session_manager.get_messages_for_model()
            rag_context = rag_manager.get_context_for_ai()
            
            if rag_context:
                enhanced_content = f"{message}\n\n游戏状态:\n{rag_context}"
                messages[-1]["content"] = enhanced_content
            
            response = model_manager.generate_response(messages)
            
            # 添加AI回应到会话
            session_manager.add_message("assistant", response)
            rag_manager.analyze_message_for_updates("assistant", response)
            
            # 更新历史记录
            history.append([message, response])
            
            return "", history
            
        except Exception as e:
            error_msg = f"❌ 处理消息时出错: {str(e)}"
            history.append([message, error_msg])
            return "", history
    
    # 自定义CSS样式 - 真正的ChatGPT风格
    css = """
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    
    .gradio-container {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif !important;
        background: #0f0f0f !important;
        color: #ffffff !important;
    }
    
    /* 主标题样式 */
    .main-header {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
        color: #ffffff;
        text-align: center;
        padding: 30px;
        border-radius: 12px;
        margin-bottom: 20px;
        border: 1px solid #333;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }
    
    .main-header h1 {
        font-size: 2.5em;
        font-weight: 700;
        margin: 0;
        background: linear-gradient(45deg, #10a37f, #1a73e8);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    /* 聊天界面样式 */
    .chatbot {
        background: #1a1a1a !important;
        border: 1px solid #333 !important;
        border-radius: 12px !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
    }
    
    /* 消息气泡样式 */
    .message {
        margin: 10px 0 !important;
        padding: 0 !important;
    }
    
    .message.user {
        text-align: right !important;
    }
    
    .message.bot {
        text-align: left !important;
    }
    
    /* 输入框样式 */
    .input-container {
        background: #1a1a1a !important;
        border: 2px solid #333 !important;
        border-radius: 25px !important;
        padding: 5px !important;
    }
    
    .input-box {
        background: transparent !important;
        border: none !important;
        color: #ffffff !important;
        font-family: 'Inter', sans-serif !important;
        font-size: 16px !important;
        padding: 12px 20px !important;
    }
    
    .input-box:focus {
        outline: none !important;
        box-shadow: none !important;
    }
    
    /* 发送按钮样式 */
    .send-button {
        background: linear-gradient(45deg, #10a37f, #1a73e8) !important;
        border: none !important;
        border-radius: 50% !important;
        color: white !important;
        width: 45px !important;
        height: 45px !important;
        font-size: 18px !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
    }
    
    .send-button:hover {
        transform: scale(1.05) !important;
        box-shadow: 0 4px 15px rgba(16, 163, 127, 0.4) !important;
    }
    
    /* 侧边栏样式 */
    .sidebar {
        background: #1a1a1a !important;
        border: 1px solid #333 !important;
        border-radius: 12px !important;
        padding: 20px !important;
        color: #ffffff !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
    }
    
    .sidebar h3 {
        color: #10a37f !important;
        margin-top: 0 !important;
        font-weight: 600 !important;
    }
    
    /* 按钮样式 */
    .control-button {
        background: linear-gradient(45deg, #2d2d2d, #404040) !important;
        border: 1px solid #555 !important;
        border-radius: 8px !important;
        color: #ffffff !important;
        padding: 10px 15px !important;
        margin: 5px 0 !important;
        width: 100% !important;
        font-family: 'Inter', sans-serif !important;
        font-weight: 500 !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
    }
    
    .control-button:hover {
        background: linear-gradient(45deg, #404040, #555) !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3) !important;
    }
    
    /* 状态显示框样式 */
    .status-box {
        background: #0f0f0f !important;
        border: 1px solid #333 !important;
        border-radius: 8px !important;
        color: #ffffff !important;
        font-family: 'Inter', monospace !important;
        font-size: 14px !important;
        line-height: 1.5 !important;
    }
    """
    
    # 创建界面
    with gr.Blocks(css=css, title="AI游戏主持人 - Elder Scrolls冒险", theme=gr.themes.Dark()) as demo:
        # 主标题
        gr.HTML("""
        <div class="main-header">
            <h1>🎮 AI游戏主持人</h1>
            <h2>Elder Scrolls冒险</h2>
            <p style="font-size: 1.2em; margin: 10px 0 0 0; opacity: 0.9;">沉浸式AI驱动的角色扮演游戏体验</p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column(scale=4):
                # ChatGPT风格的聊天界面
                chatbot = gr.Chatbot(
                    [],
                    elem_id="chatbot",
                    bubble_full_width=False,
                    height=600,
                    show_copy_button=True,
                    layout="panel",
                    elem_classes=["chatbot"],
                    avatar_images=(
                        "https://cdn-icons-png.flaticon.com/512/3135/3135715.png",  # 用户头像
                        "https://cdn-icons-png.flaticon.com/512/4712/4712109.png"   # AI头像
                    )
                )
                
                with gr.Row(elem_classes=["input-container"]):
                    msg = gr.Textbox(
                        placeholder="描述您的行动或对话...",
                        container=False,
                        scale=7,
                        elem_classes=["input-box"],
                        lines=1,
                        max_lines=3
                    )
                    submit = gr.Button(
                        "🚀",
                        scale=1,
                        elem_classes=["send-button"],
                        size="sm"
                    )
            
            with gr.Column(scale=1):
                with gr.Group(elem_classes=["sidebar"]):
                    gr.HTML('<h3>🎛️ 游戏控制</h3>')
                    
                    # 游戏状态显示
                    status_display = gr.Textbox(
                        label="📊 游戏状态",
                        lines=12,
                        interactive=False,
                        elem_classes=["status-box"]
                    )
                    
                    # 控制按钮
                    refresh_btn = gr.Button("🔄 刷新状态", elem_classes=["control-button"])
                    save_btn = gr.Button("💾 保存游戏", elem_classes=["control-button"])
                    export_btn = gr.Button("📄 导出DOCX", elem_classes=["control-button"])
                    clear_btn = gr.Button("🗑️ 清空对话", elem_classes=["control-button"])
                    
                    # 操作结果显示
                    info_display = gr.Textbox(
                        label="ℹ️ 操作结果",
                        lines=4,
                        interactive=False,
                        elem_classes=["status-box"]
                    )
        
        # 事件绑定
        msg.submit(respond, [msg, chatbot], [msg, chatbot])
        submit.click(respond, [msg, chatbot], [msg, chatbot])
        
        # 功能按钮事件
        def update_status():
            session_summary = session_manager.get_conversation_summary()
            rag_summary = rag_manager.get_summary()
            rag_context = rag_manager.get_context_for_ai()
            
            status = f"""
📊 会话统计:
• 总消息: {session_summary['total_messages']}
• 玩家消息: {session_summary['user_messages']}
• AI回应: {session_summary['assistant_messages']}

🎮 游戏状态:
• 库存物品: {rag_summary['inventory_items']}
• 金币: {rag_summary['gold']}
• 当前位置: {rag_summary['current_location'] or '未知'}
• 关键事件: {rag_summary['key_events']}
• 玩家选择: {rag_summary['player_choices']}

🔍 上下文信息:
{rag_context[:300] + '...' if len(rag_context) > 300 else rag_context}
            """.strip()
            return status
        
        def save_game():
            try:
                json_path = session_manager.save_session()
                return f"✅ 游戏已保存\n📁 {json_path}"
            except Exception as e:
                return f"❌ 保存失败: {str(e)}"
        
        def export_docx():
            try:
                docx_path = session_manager.export_to_docx()
                return f"✅ DOCX已导出\n📄 {docx_path}"
            except Exception as e:
                return f"❌ 导出失败: {str(e)}"
        
        def clear_chat():
            # 重新初始化管理器
            global session_manager, rag_manager
            session_manager = SessionManager()
            rag_manager = RAGManager()
            return "✅ 对话已清空\n🎮 游戏重新开始", []
        
        refresh_btn.click(update_status, outputs=status_display)
        save_btn.click(save_game, outputs=info_display)
        export_btn.click(export_docx, outputs=info_display)
        clear_btn.click(clear_chat, outputs=[info_display, chatbot])
        
        # 初始化状态显示
        demo.load(update_status, outputs=status_display)
    
    return demo

# 启动ChatGPT风格界面
if model_manager.model:
    print("🚀 启动ChatGPT风格聊天界面...")
    
    try:
        demo = create_chatgpt_interface()
        demo.launch(
            share=True,
            debug=False,
            show_error=True,
            inbrowser=True,
            server_name="0.0.0.0",
            server_port=7860
        )
    except Exception as e:
        print(f"❌ 界面启动失败: {str(e)}")
        print("请检查Gradio版本和依赖包")
        print("\n🔄 尝试简化版界面...")
        
        # 简化版界面
        def simple_chat():
            print("\n" + "="*60)
            print("🎮 Elder Scrolls AI冒险 - 简化版聊天界面")
            print("="*60)
            print("输入 'quit' 或 'exit' 退出游戏")
            print("输入 'save' 保存游戏")
            print("输入 'status' 查看状态")
            print("-"*60)
            
            while True:
                try:
                    user_input = input("\n🎮 您的行动: ").strip()
                    
                    if user_input.lower() in ['quit', 'exit', '退出']:
                        print("👋 感谢游玩！再见！")
                        break
                    elif user_input.lower() in ['save', '保存']:
                        filepath = session_manager.save_session()
                        print(f"💾 游戏已保存到: {filepath}")
                        continue
                    elif user_input.lower() in ['status', '状态']:
                        summary = session_manager.get_conversation_summary()
                        rag_summary = rag_manager.get_summary()
                        print(f"\n📊 游戏状态:")
                        print(f"- 总消息数: {summary['total_messages']}")
                        print(f"- 库存物品: {rag_summary['inventory_items']}")
                        print(f"- 金币: {rag_summary['gold']}")
                        print(f"- 关键事件: {rag_summary['key_events']}")
                        continue
                    elif not user_input:
                        print("❌ 请输入您的行动！")
                        continue
                    
                    # 处理用户输入
                    session_manager.add_message("user", user_input)
                    rag_manager.analyze_message_for_updates("user", user_input)
                    
                    # 获取AI回应
                    messages = session_manager.get_messages_for_model()
                    rag_context = rag_manager.get_context_for_ai()
                    
                    if rag_context:
                        enhanced_content = f"{user_input}\n\n游戏状态:\n{rag_context}"
                        messages[-1]["content"] = enhanced_content
                    
                    response = model_manager.generate_response(messages)
                    session_manager.add_message("assistant", response)
                    rag_manager.analyze_message_for_updates("assistant", response)
                    
                    print(f"\n🎭 游戏主持人: {response}")
                    
                except KeyboardInterrupt:
                    print("\n👋 游戏被中断，再见！")
                    break
                except Exception as e:
                    print(f"❌ 处理消息时出错: {str(e)}")
        
        simple_chat()
else:
    print("❌ 请先加载模型！")
    print("请运行上方的'加载AI模型'单元格")