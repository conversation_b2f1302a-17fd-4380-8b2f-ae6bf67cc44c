# 10-1_chat_interface_core.py
# 聊天界面核心功能 - 消息处理和基础功能

import gradio as gr
from typing import List, Tuple, Optional, Dict

# 检查依赖模块是否已加载
def check_dependencies():
    """检查必要的依赖模块是否已加载"""
    required_modules = ['config', 'session_manager', 'rag_system', 'script_manager', 'model_manager', 'export_manager', 'RAGDocument']
    missing_modules = []
    available_modules = []

    for module_name in required_modules:
        try:
            # 尝试访问模块
            module_obj = globals().get(module_name)
            if module_obj is None:
                missing_modules.append(module_name)
            else:
                available_modules.append(module_name)
        except Exception as e:
            missing_modules.append(f"{module_name}({e})")

    print(f"✅ 可用模块: {', '.join(available_modules)}")

    if missing_modules:
        print(f"⚠️  缺少依赖模块: {', '.join(missing_modules)}")
        print("💡 请确保按顺序执行了前面的所有模块文件")
        return False

    return True

# 检查依赖
dependency_check_result = check_dependencies()
if not dependency_check_result:
    print("❌ 依赖检查失败，聊天界面核心功能可能无法正常工作")
    print("🔧 将尝试继续加载，但某些功能可能不可用")
else:
    print("✅ 依赖检查通过")

class ChatInterfaceCore:
    """聊天界面核心功能"""
    
    def __init__(self):
        self.conversation_history = []
    
    def chat_response(self, message: str, history: List[Tuple[str, str]]) -> Tuple[str, List[Tuple[str, str]]]:
        """处理聊天消息并返回AI回复"""
        try:
            # 添加用户消息到进度管理器
            session_manager.add_message("user", message)
            
            # 分析用户消息（RAG系统）
            try:
                rag_system.analyze_message("user", message)
            except Exception as e:
                print(f"⚠️  RAG分析失败: {e}")
            
            # 获取AI回复
            if config.is_development_stage_1():
                # 第一阶段：使用JSON剧本
                ai_response = script_manager.get_contextual_response(message, history)
            else:
                # 第二阶段：使用LLM
                if model_manager and model_manager.model_loaded:
                    ai_response = model_manager.generate_response(
                        message,
                        max_tokens=config.model_config['max_tokens'],
                        temperature=config.model_config['temperature'],
                        top_p=config.model_config['top_p']
                    )
                else:
                    ai_response = "❌ 模型未加载，请先加载模型。使用 model_manager.load_model() 加载模型。"
            
            # 添加AI回复到进度管理器
            session_manager.add_message("assistant", ai_response)
            
            # 分析AI回复（RAG系统）
            try:
                rag_system.analyze_message("assistant", ai_response)
            except Exception as e:
                print(f"⚠️  RAG分析失败: {e}")
            
            # 自动保存（每5条消息）
            session_manager.auto_save_counter += 1
            if session_manager.auto_save_counter >= 5:
                session_manager.auto_save_current_session()
                session_manager.auto_save_counter = 0
            
            # 更新历史记录
            history.append((message, ai_response))
            
            return "", history
            
        except Exception as e:
            error_msg = f"❌ 处理消息时出错: {str(e)}"
            history.append((message, error_msg))
            return "", history
    
    def clear_chat(self) -> Tuple[List[Dict], str]:
        """安全重新开始：先自动保存当前进度"""
        try:
            # 检查当前进度是否有内容
            current_stats = session_manager.get_session_stats()

            if current_stats['total_messages'] > 0:
                # 自动保存当前进度
                try:
                    saved_path = session_manager.save_session()
                    backup_msg = f"💾 当前进度已自动保存: {saved_path.split('/')[-1]}\n"
                except Exception as e:
                    backup_msg = f"⚠️  自动保存失败: {e}\n"
            else:
                backup_msg = ""

            # 清空当前进度
            session_manager.clear_current_session()

            # 重置系统状态
            try:
                script_manager.reset_conversation()
                rag_system.rag_document = RAGDocument()
                rag_system.update_counter = 0
                reset_msg = "🔄 系统状态已重置\n"
            except Exception as e:
                reset_msg = f"⚠️  系统重置失败: {e}\n"

            result_msg = backup_msg + reset_msg + "🔄 游戏已重新开始，开始新的冒险！"
            return [], result_msg

        except Exception as e:
            return [], f"❌ 重新开始操作失败: {str(e)}"
    
    def export_json_client(self) -> str:
        """导出客户格式JSON"""
        try:
            filepath = export_manager.export_client_json()
            if filepath:
                filename = filepath.split('/')[-1]
                return f"✅ 客户JSON导出成功!\n📄 文件: {filename}\n📂 位置: exports/"
            else:
                return "❌ 导出失败，请检查对话内容"
        except Exception as e:
            return f"❌ 导出JSON失败: {str(e)}"
    
    def export_docx(self) -> str:
        """导出DOCX文档"""
        try:
            filepath = export_manager.export_docx()
            if filepath:
                filename = filepath.split('/')[-1]
                return f"✅ DOCX导出成功!\n📄 文件: {filename}\n📂 位置: exports/"
            else:
                return "❌ 导出失败，请检查对话内容"
        except Exception as e:
            return f"❌ 导出DOCX失败: {str(e)}"
    
    def export_game_summary(self) -> str:
        """导出游戏总结"""
        try:
            filepath = export_manager.export_game_summary()
            if filepath:
                filename = filepath.split('/')[-1]
                return f"✅ 游戏总结导出成功!\n📄 文件: {filename}\n📂 位置: exports/"
            else:
                return "❌ 导出失败，请检查游戏内容"
        except Exception as e:
            return f"❌ 导出游戏总结失败: {str(e)}"
    
    def get_rag_context(self) -> str:
        """获取RAG上下文信息"""
        try:
            return rag_system.get_context_summary()
        except Exception as e:
            return f"❌ 获取游戏状态失败: {str(e)}"
    
    def get_session_stats(self) -> str:
        """获取进度统计信息"""
        stats = session_manager.get_session_stats()
        rag_stats = rag_system.get_stats()
        
        stats_text = f"""
📊 **进度统计**
• 进度ID: {stats['session_id'][:8]}...
• 总消息数: {stats['total_messages']}
• 用户消息: {stats['user_messages']}
• AI回复: {stats['assistant_messages']}
• 创建时间: {stats['created_at'][:19]}

📋 **游戏状态**
• 物品数量: {rag_stats['total_items']}
• 事件记录: {rag_stats['total_events']}
• 重要事件: {rag_stats['high_importance_events']}
• 玩家选择: {rag_stats['player_choices']}

🎮 **开发状态**
• 开发阶段: {config.development_stage}
• 使用模拟回复: {config.use_mock_responses}
        """
        return stats_text.strip()
    
    def get_conversation_history(self) -> str:
        """获取当前对话历史"""
        try:
            messages = session_manager.current_conversation.get_messages()
            if not messages:
                return "📝 当前进度暂无对话记录"

            history_text = f"📜 **当前进度历史** (共{len(messages)}条消息)\n"
            history_text += f"进度ID: {session_manager.current_conversation.session_id[:8]}...\n\n"

            for i, msg in enumerate(messages, 1):
                role_emoji = "👤" if msg.role == "user" else "🎮"
                role_name = "玩家" if msg.role == "user" else "AI主持人"
                timestamp = msg.timestamp.strftime("%H:%M:%S")

                history_text += f"{role_emoji} **{role_name} #{i}** ({timestamp})\n"
                # 限制每条消息的显示长度
                content = msg.content[:200] + "..." if len(msg.content) > 200 else msg.content
                history_text += f"{content}\n\n"

            return history_text

        except Exception as e:
            return f"❌ 获取对话历史失败: {str(e)}"

# 创建核心功能实例
try:
    chat_core = ChatInterfaceCore()
    print("💬 聊天界面核心功能加载完成")
    print("🎮 准备加载会话管理功能...")
except Exception as e:
    print(f"❌ 创建聊天界面核心功能失败: {e}")
    print("💡 请检查前面的模块是否都正确执行")
    chat_core = None
