# 10-2_chat_interface_session.py
# 聊天界面会话管理功能 - 进度保存、加载、列表

# 检查依赖模块是否已加载
def check_session_dependencies():
    """检查会话管理所需的依赖模块"""
    required_modules = ['session_manager', 'rag_system', 'script_manager', 'RAGDocument']
    missing_modules = []

    for module_name in required_modules:
        if module_name not in globals():
            missing_modules.append(module_name)

    if missing_modules:
        print(f"⚠️  会话管理模块缺少依赖: {', '.join(missing_modules)}")
        return False

    return True

# 检查依赖
if not check_session_dependencies():
    print("❌ 会话管理依赖检查失败")
else:
    print("✅ 会话管理依赖检查通过")

class ChatInterfaceSession:
    """聊天界面会话管理功能"""
    
    def list_saved_sessions(self) -> str:
        """列出所有保存的存档"""
        try:
            sessions = session_manager.list_saved_sessions()
            if not sessions:
                return "📁 暂无保存的存档文件"

            sessions_text = f"📁 **游戏存档** (共{len(sessions)}个)\n\n"

            for i, session in enumerate(sessions[:10], 1):  # 只显示最近10个
                filename = session['filename']
                message_count = session['message_count']
                file_size = session['file_size'] // 1024  # KB

                # 提取时间信息
                if session['last_updated']:
                    try:
                        from datetime import datetime
                        last_updated = datetime.fromisoformat(session['last_updated'])
                        time_str = last_updated.strftime("%m-%d %H:%M")
                    except:
                        time_str = "未知时间"
                else:
                    time_str = "未知时间"

                sessions_text += f"**{i}.** {filename}\n"
                sessions_text += f"   📊 {message_count}条消息 | 📅 {time_str} | 💾 {file_size}KB\n\n"

            if len(sessions) > 10:
                sessions_text += f"... 还有 {len(sessions) - 10} 个存档文件\n"

            sessions_text += "\n💡 提示: 可以通过进度管理功能加载历史存档"
            return sessions_text

        except Exception as e:
            return f"❌ 获取存档列表失败: {str(e)}"
    
    def load_session_by_name(self, filename: str) -> str:
        """安全加载进度：先保存当前进度，再清空，最后加载"""
        try:
            if not filename.strip():
                return "❌ 请输入进度文件名"

            # 步骤1：检查当前进度是否有内容
            current_stats = session_manager.get_session_stats()
            if current_stats['total_messages'] > 0:
                # 自动保存当前进度
                try:
                    saved_path = session_manager.save_session()
                    backup_msg = f"💾 当前进度已自动保存: {saved_path.split('/')[-1]}\n"
                except Exception as e:
                    backup_msg = f"⚠️  当前进度保存失败: {e}\n"
            else:
                backup_msg = "📝 当前进度为空，无需保存\n"

            # 步骤2：查找目标存档文件
            sessions = session_manager.list_saved_sessions()
            target_session = None

            for session in sessions:
                if filename in session['filename'] or session['filename'] == filename:
                    target_session = session
                    break

            if not target_session:
                return f"{backup_msg}❌ 未找到存档文件: {filename}"

            # 步骤3：清空当前进度
            try:
                session_manager.clear_current_session()
                clear_msg = "🧹 当前进度已清空\n"
            except Exception as e:
                return f"{backup_msg}❌ 清空进度失败: {e}"

            # 步骤4：重置系统状态
            try:
                # 重置剧本管理器
                script_manager.reset_conversation()

                # 重置RAG系统
                rag_system.rag_document = RAGDocument()
                rag_system.update_counter = 0

                reset_msg = "🔄 系统状态已重置\n"
            except Exception as e:
                reset_msg = f"⚠️  系统重置部分失败: {e}\n"

            # 步骤5：加载目标存档
            try:
                success = session_manager.load_session(target_session['filepath'])
                if not success:
                    return f"{backup_msg}{clear_msg}{reset_msg}❌ 存档文件加载失败"

                load_msg = "📂 存档文件加载成功\n"
            except Exception as e:
                return f"{backup_msg}{clear_msg}{reset_msg}❌ 加载过程出错: {e}"

            # 步骤6：重新分析历史消息（RAG）
            try:
                messages = session_manager.current_conversation.get_messages()
                if messages:
                    for msg in messages:
                        try:
                            rag_system.analyze_message(msg.role, msg.content)
                        except:
                            continue  # 忽略单条消息的分析错误

                    # 调整剧本索引
                    ai_message_count = len([m for m in messages if m.role == 'assistant'])
                    script_manager.current_index = ai_message_count

                    sync_msg = f"🔄 已重新分析 {len(messages)} 条消息\n"
                else:
                    sync_msg = "📝 加载的存档为空\n"

            except Exception as e:
                sync_msg = f"⚠️  消息重新分析失败: {e}\n"

            # 步骤7：验证加载结果
            try:
                final_stats = session_manager.get_session_stats()
                result_msg = f"✅ 进度加载完成!\n"
                result_msg += f"📊 包含 {final_stats['total_messages']} 条消息\n"
                result_msg += f"🆔 进度ID: {final_stats['session_id'][:8]}...\n"
                result_msg += f"💡 系统已准备就绪，可以继续对话"

                return backup_msg + clear_msg + reset_msg + load_msg + sync_msg + result_msg

            except Exception as e:
                return f"{backup_msg}{clear_msg}{reset_msg}{load_msg}{sync_msg}⚠️  加载验证失败: {e}"

        except Exception as e:
            return f"❌ 进度加载过程出现严重错误: {str(e)}"

    def save_current_session(self) -> str:
        """手动保存当前进度"""
        try:
            current_stats = session_manager.get_session_stats()

            if current_stats['total_messages'] == 0:
                return "📝 当前进度为空，无需保存"

            saved_path = session_manager.save_session()
            filename = saved_path.split('/')[-1]

            return f"✅ 进度保存成功!\n💾 文件名: {filename}\n📊 包含 {current_stats['total_messages']} 条消息"

        except Exception as e:
            return f"❌ 保存进度失败: {str(e)}"

# 创建会话管理功能实例
chat_session = ChatInterfaceSession()

print("💾 聊天界面会话管理功能加载完成")
print("🤖 准备加载模型管理功能...")
