# 10-4_chat_interface_ui.py
# 聊天界面UI创建和事件绑定 - Gradio界面构建

import gradio as gr

# 检查依赖模块是否已加载
def check_ui_dependencies():
    """检查UI所需的依赖模块"""
    required_modules = ['config', 'chat_core', 'chat_session', 'chat_model']
    missing_modules = []

    for module_name in required_modules:
        if module_name not in globals():
            missing_modules.append(module_name)

    if missing_modules:
        print(f"⚠️  UI模块缺少依赖: {', '.join(missing_modules)}")
        return False

    return True

# 检查依赖
if not check_ui_dependencies():
    print("❌ UI依赖检查失败")
else:
    print("✅ UI依赖检查通过")

class ChatInterfaceUI:
    """聊天界面UI创建和事件绑定"""
    
    def __init__(self):
        self.interface = None
    
    def create_interface(self) -> gr.Blocks:
        """创建Gradio界面"""
        with gr.Blocks(title="AI游戏主持人", theme=gr.themes.Soft()) as interface:
            # 标题和说明
            gr.Markdown("# 🎮 AI游戏主持人")
            gr.Markdown("Elder Scrolls风格的RPG游戏体验")

            # 开发阶段提示
            try:
                if config.is_development_stage_1():
                    gr.Markdown("🚧 **开发阶段1**: 当前使用JSON剧本模拟AI回复，测试界面和功能")
                else:
                    gr.Markdown("🤖 **开发阶段2**: 当前使用LLM模型生成真实AI回复")
            except:
                gr.Markdown("🚧 **开发阶段1**: 功能测试模式")

            # 聊天界面
            chatbot = gr.Chatbot(
                label="游戏对话",
                height=400,
                type='messages',
                show_copy_button=True
            )

            # 输入区域
            with gr.Row():
                msg_input = gr.Textbox(
                    label="输入你的行动",
                    placeholder="描述你想要做的事情...",
                    scale=4,
                    lines=2
                )
                send_btn = gr.Button("发送", variant="primary", scale=1)

            # 功能按钮
            with gr.Row():
                clear_btn = gr.Button("🔄 重新开始")
                stats_btn = gr.Button("📊 查看统计")
                context_btn = gr.Button("📋 游戏状态")
                history_btn = gr.Button("📜 对话历史")
                sessions_btn = gr.Button("📁 存档列表")

            # 信息显示区域
            with gr.Row():
                stats_display = gr.Textbox(
                    label="进度统计",
                    lines=8,
                    interactive=False,
                    scale=1
                )
                context_display = gr.Textbox(
                    label="游戏状态",
                    lines=8,
                    interactive=False,
                    scale=1
                )

            # 历史对话显示区域
            with gr.Row():
                history_display = gr.Textbox(
                    label="对话历史",
                    lines=10,
                    interactive=False,
                    scale=2
                )
                sessions_display = gr.Textbox(
                    label="存档列表",
                    lines=10,
                    interactive=False,
                    scale=1
                )

            # 进度管理功能
            gr.Markdown("### 📁 进度管理")
            with gr.Row():
                session_input = gr.Textbox(
                    label="存档文件名",
                    placeholder="输入存档文件名加载历史进度...",
                    scale=2
                )
                load_session_btn = gr.Button("📂 加载进度", scale=1)
                save_session_btn = gr.Button("💾 保存进度", scale=1)

            session_status = gr.Textbox(
                label="进度操作状态",
                lines=3,
                interactive=False
            )

            # 模型管理功能
            gr.Markdown("### 🤖 模型管理")
            with gr.Row():
                model_status_display = gr.Textbox(
                    label="模型状态",
                    lines=6,
                    interactive=False,
                    scale=2
                )

            with gr.Row():
                stage1_btn = gr.Button("📜 切换到阶段1（JSON剧本）", scale=1)
                stage2_btn = gr.Button("🤖 切换到阶段2（LLM模式）", scale=1)
                load_model_btn = gr.Button("📥 加载模型", variant="primary", scale=1)
                unload_model_btn = gr.Button("🗑️ 卸载模型", scale=1)

            with gr.Row():
                test_model_btn = gr.Button("🧪 测试模型", scale=1)
                model_info_btn = gr.Button("📋 详细信息", scale=1)

            model_operation_status = gr.Textbox(
                label="模型操作状态",
                lines=4,
                interactive=False
            )

            # 导出功能
            gr.Markdown("### 📤 导出功能")
            with gr.Row():
                export_json_btn = gr.Button("📄 导出JSON")
                export_docx_btn = gr.Button("📝 导出DOCX")
                export_summary_btn = gr.Button("📋 游戏总结")

            export_status = gr.Textbox(
                label="导出状态",
                lines=2,
                interactive=False
            )
            
            # 事件绑定
            self._bind_events(
                msg_input, send_btn, chatbot, clear_btn, stats_btn, context_btn,
                history_btn, sessions_btn, stats_display, context_display,
                history_display, sessions_display, session_input, load_session_btn,
                save_session_btn, session_status, model_status_display, stage1_btn,
                stage2_btn, load_model_btn, unload_model_btn, test_model_btn,
                model_info_btn, model_operation_status, export_json_btn,
                export_docx_btn, export_summary_btn, export_status
            )
        
        self.interface = interface
        return interface
    
    def _bind_events(self, msg_input, send_btn, chatbot, clear_btn, stats_btn, 
                    context_btn, history_btn, sessions_btn, stats_display, 
                    context_display, history_display, sessions_display, 
                    session_input, load_session_btn, save_session_btn, 
                    session_status, model_status_display, stage1_btn, stage2_btn,
                    load_model_btn, unload_model_btn, test_model_btn, model_info_btn,
                    model_operation_status, export_json_btn, export_docx_btn, 
                    export_summary_btn, export_status):
        """绑定所有事件"""
        
        # 聊天事件
        msg_input.submit(
            chat_core.chat_response,
            inputs=[msg_input, chatbot],
            outputs=[msg_input, chatbot]
        )

        send_btn.click(
            chat_core.chat_response,
            inputs=[msg_input, chatbot],
            outputs=[msg_input, chatbot]
        )
        
        clear_btn.click(
            chat_core.clear_chat,
            outputs=[chatbot, export_status]
        )
        
        # 信息显示事件
        stats_btn.click(
            chat_core.get_session_stats,
            outputs=[stats_display]
        )
        
        context_btn.click(
            chat_core.get_rag_context,
            outputs=[context_display]
        )

        history_btn.click(
            chat_core.get_conversation_history,
            outputs=[history_display]
        )

        sessions_btn.click(
            chat_session.list_saved_sessions,
            outputs=[sessions_display]
        )

        # 会话管理事件
        load_session_btn.click(
            chat_session.load_session_by_name,
            inputs=[session_input],
            outputs=[session_status]
        )

        save_session_btn.click(
            chat_session.save_current_session,
            outputs=[session_status]
        )

        # 模型管理事件
        stage1_btn.click(
            chat_model.switch_to_stage_1,
            outputs=[model_operation_status]
        )

        stage2_btn.click(
            chat_model.switch_to_stage_2,
            outputs=[model_operation_status]
        )

        load_model_btn.click(
            chat_model.load_model,
            outputs=[model_operation_status]
        )

        unload_model_btn.click(
            chat_model.unload_model,
            outputs=[model_operation_status]
        )

        test_model_btn.click(
            chat_model.test_model,
            outputs=[model_operation_status]
        )

        model_info_btn.click(
            chat_model.get_model_info_detailed,
            outputs=[model_operation_status]
        )

        # 导出事件
        export_json_btn.click(
            chat_core.export_json_client,
            outputs=[export_status]
        )
        
        export_docx_btn.click(
            chat_core.export_docx,
            outputs=[export_status]
        )
        
        export_summary_btn.click(
            chat_core.export_game_summary,
            outputs=[export_status]
        )

        # 初始化显示
        self.interface.load(
            chat_core.get_session_stats,
            outputs=[stats_display]
        )

        self.interface.load(
            chat_model.get_model_status,
            outputs=[model_status_display]
        )

        self.interface.load(
            lambda: "🎮 欢迎来到AI游戏主持人！开始你的冒险吧！",
            outputs=[context_display]
        )

# 创建UI管理器实例
chat_ui = ChatInterfaceUI()

# 创建完整的聊天界面类（整合所有功能）
class ChatInterface:
    """完整的聊天界面类 - 整合所有功能模块"""
    
    def __init__(self):
        self.core = chat_core
        self.session = chat_session
        self.model = chat_model
        self.ui = chat_ui
    
    def create_interface(self):
        """创建界面的统一入口"""
        return self.ui.create_interface()
    
    # 代理所有核心方法
    def chat_response(self, message, history):
        return self.core.chat_response(message, history)
    
    def clear_chat(self):
        return self.core.clear_chat()
    
    def get_session_stats(self):
        return self.core.get_session_stats()
    
    def get_conversation_history(self):
        return self.core.get_conversation_history()
    
    def get_rag_context(self):
        return self.core.get_rag_context()

# 创建全局聊天界面实例
chat_interface = ChatInterface()

print("🎨 聊天界面UI创建功能加载完成")
print("✅ 所有聊天界面模块加载完成")
print("🎮 准备创建主应用...")
