# 07_export_manager.py
# 导出管理器 - 处理JSON和DOCX导出

import os
import json
from datetime import datetime
from typing import Dict, Any, Optional
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH

class ExportManager:
    """导出管理器 - 处理各种格式的导出"""
    
    def __init__(self, exports_path: str):
        self.exports_path = exports_path
        
        # 确保目录存在
        os.makedirs(exports_path, exist_ok=True)
    
    def export_conversation_json(self, conversation: Conversation, 
                                format_type: str = "client") -> str:
        """导出对话为JSON格式"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if format_type == "client":
            # 客户要求的简单格式
            data = conversation.get_conversation_json()
            filename = f"conversation_client_{timestamp}.json"
        else:
            # 完整格式（包含元数据）
            data = conversation.get_full_json()
            filename = f"conversation_full_{timestamp}.json"
        
        filepath = os.path.join(self.exports_path, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"📄 JSON导出成功: {filename}")
            return filepath
            
        except Exception as e:
            print(f"❌ JSON导出失败: {e}")
            return ""
    
    def export_conversation_docx(self, conversation: Conversation) -> str:
        """导出对话为DOCX格式"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"conversation_{timestamp}.docx"
        filepath = os.path.join(self.exports_path, filename)
        
        try:
            # 创建文档
            doc = Document()
            
            # 添加标题
            title = doc.add_heading('🎮 AI游戏主持人对话记录', 0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # 添加会话信息
            info_para = doc.add_paragraph()
            info_para.add_run('会话ID: ').bold = True
            info_para.add_run(conversation.session_id)
            info_para.add_run('\n创建时间: ').bold = True
            info_para.add_run(conversation.created_at.strftime("%Y-%m-%d %H:%M:%S"))
            info_para.add_run('\n最后更新: ').bold = True
            info_para.add_run(conversation.last_updated.strftime("%Y-%m-%d %H:%M:%S"))
            info_para.add_run('\n消息总数: ').bold = True
            info_para.add_run(str(len(conversation.messages)))
            
            # 添加分隔线
            doc.add_paragraph('─' * 50)
            
            # 添加对话内容
            for i, message in enumerate(conversation.messages, 1):
                # 添加消息头
                if message.role == 'user':
                    header = doc.add_paragraph()
                    header.add_run(f'👤 玩家 #{i}').bold = True
                    header.add_run(f' ({message.timestamp.strftime("%H:%M:%S")})')
                else:
                    header = doc.add_paragraph()
                    header.add_run(f'🎮 AI主持人 #{i}').bold = True
                    header.add_run(f' ({message.timestamp.strftime("%H:%M:%S")})')
                
                # 添加消息内容
                content_para = doc.add_paragraph(message.content)
                content_para.style = 'Quote'
                
                # 添加空行
                doc.add_paragraph()
            
            # 添加页脚信息
            doc.add_paragraph('─' * 50)
            footer = doc.add_paragraph()
            footer.add_run('导出时间: ').bold = True
            footer.add_run(datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
            footer.add_run('\n导出工具: AI游戏主持人系统')
            
            # 保存文档
            doc.save(filepath)
            
            print(f"📝 DOCX导出成功: {filename}")
            return filepath
            
        except Exception as e:
            print(f"❌ DOCX导出失败: {e}")
            return ""
    
    def export_rag_documents(self, rag_doc: RAGDocument) -> str:
        """导出RAG文档为JSON格式"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"rag_documents_{timestamp}.json"
        filepath = os.path.join(self.exports_path, filename)
        
        try:
            data = rag_doc.to_dict()
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"📋 RAG文档导出成功: {filename}")
            return filepath
            
        except Exception as e:
            print(f"❌ RAG文档导出失败: {e}")
            return ""
    
    def create_game_summary_docx(self, conversation: Conversation, 
                                rag_doc: RAGDocument) -> str:
        """创建游戏总结DOCX文档"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"game_summary_{timestamp}.docx"
        filepath = os.path.join(self.exports_path, filename)
        
        try:
            doc = Document()
            
            # 标题
            title = doc.add_heading('🎮 游戏冒险总结', 0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # 基本信息
            doc.add_heading('📊 基本信息', level=1)
            info_para = doc.add_paragraph()
            info_para.add_run('会话ID: ').bold = True
            info_para.add_run(conversation.session_id)
            info_para.add_run('\n游戏时长: ').bold = True
            duration = conversation.last_updated - conversation.created_at
            info_para.add_run(str(duration))
            info_para.add_run('\n对话轮数: ').bold = True
            info_para.add_run(str(len(conversation.messages) // 2))
            
            # 物品清单
            doc.add_heading('🎒 物品清单', level=1)
            if rag_doc.inventory:
                for item in rag_doc.inventory:
                    item_para = doc.add_paragraph()
                    item_para.add_run(f'• {item.name}').bold = True
                    item_para.add_run(f' x{item.quantity}')
                    if item.description:
                        item_para.add_run(f' - {item.description}')
            else:
                doc.add_paragraph('暂无物品记录')
            
            # 重要事件
            doc.add_heading('📜 重要事件', level=1)
            if rag_doc.key_events:
                for event in rag_doc.key_events:
                    event_para = doc.add_paragraph()
                    importance_emoji = {'high': '🔥', 'medium': '⭐', 'low': '📌'}
                    emoji = importance_emoji.get(event.importance, '📌')
                    event_para.add_run(f'{emoji} ').bold = True
                    event_para.add_run(event.event)
                    if event.game_time:
                        event_para.add_run(f' ({event.game_time})')
            else:
                doc.add_paragraph('暂无事件记录')
            
            # 玩家特征
            doc.add_heading('👤 玩家特征', level=1)
            prefs = rag_doc.player_preferences
            if prefs['playstyle']:
                style_para = doc.add_paragraph()
                style_para.add_run('游戏风格: ').bold = True
                style_para.add_run(prefs['playstyle'])
            
            if prefs['personality']:
                personality_para = doc.add_paragraph()
                personality_para.add_run('性格特征: ').bold = True
                personality_para.add_run(prefs['personality'])
            
            if prefs['choices']:
                doc.add_paragraph().add_run('重要选择:').bold = True
                for choice in prefs['choices'][-5:]:  # 最近5个选择
                    doc.add_paragraph(f'• {choice}')
            
            # 保存文档
            doc.save(filepath)
            
            print(f"📋 游戏总结导出成功: {filename}")
            return filepath
            
        except Exception as e:
            print(f"❌ 游戏总结导出失败: {e}")
            return ""
    
    def list_exports(self) -> list:
        """列出所有导出文件"""
        exports = []
        
        try:
            for filename in os.listdir(self.exports_path):
                if filename.endswith(('.json', '.docx')):
                    filepath = os.path.join(self.exports_path, filename)
                    file_info = {
                        'filename': filename,
                        'filepath': filepath,
                        'size': os.path.getsize(filepath),
                        'modified': datetime.fromtimestamp(os.path.getmtime(filepath))
                    }
                    exports.append(file_info)
            
            # 按修改时间排序
            exports.sort(key=lambda x: x['modified'], reverse=True)
            
        except Exception as e:
            print(f"❌ 列出导出文件失败: {e}")
        
        return exports

# 创建全局导出管理器实例
export_manager = ExportManager(config.exports_path)

print("📤 导出管理器加载完成")
print(f"📂 导出文件路径: {config.exports_path}")
print("🎮 准备加载RAG文档系统...")
