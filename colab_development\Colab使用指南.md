# Google Colab 使用指南

## 📋 快速开始

### 1. 创建新的Colab笔记本
1. 访问 [Google Colab](https://colab.research.google.com/)
2. 点击"新建笔记本"
3. 重命名为"AI_Game_Master"

### 2. 按顺序复制代码到Cell

**重要**：必须按照以下顺序执行，每个文件对应一个Cell：

```
Cell 1:  00_drive_test.py          # Google Drive连接测试
Cell 2:  01_setup_and_mount.py     # 环境设置和挂载
Cell 3:  02_dependencies_enhanced.py # 依赖安装（带进度条）
Cell 4:  03_config.py              # 配置管理
Cell 5:  04_data_models.py         # 数据结构
Cell 6:  05_json_script_manager.py # JSON剧本管理
Cell 7:  06_session_manager.py     # 会话管理
Cell 8:  07_export_manager.py      # 导出功能
Cell 9:  08_rag_document_system.py # RAG文档系统
Cell 10: 09_model_manager.py       # 模型管理
Cell 11: 10-1_chat_interface_core.py    # 聊天核心功能
Cell 12: 10-2_chat_interface_session.py # 会话管理功能
Cell 13: 10-3_chat_interface_model.py   # 模型管理功能
Cell 14: 10-4_chat_interface_ui.py      # UI创建
Cell 15: 11_main_app.py            # 主应用启动
```

### 3. 执行步骤

1. **逐个执行Cell**：从Cell 1开始，按顺序执行每个Cell
2. **观察输出**：每个Cell执行后会显示状态信息
3. **等待完成**：特别是Cell 3（依赖安装）可能需要几分钟

### 4. 新功能说明

#### 🚀 改进的依赖安装 (Cell 3)
- **进度显示**：实时显示安装进度和状态
- **分类安装**：按功能分组安装包
- **错误处理**：安装失败时显示详细错误信息
- **智能判断**：80%以上成功即认为可用

#### 🔍 依赖检查系统
- **自动检查**：每个模块会自动检查所需依赖
- **清晰提示**：缺少依赖时会明确指出
- **执行指导**：提供详细的执行顺序说明

### 5. 故障排除

#### 如果某个Cell执行失败：
1. **查看错误信息**：仔细阅读红色错误输出
2. **检查依赖**：确保前面的Cell都成功执行
3. **重新执行**：可以重新执行失败的Cell
4. **重启运行时**：如果问题严重，可以重启Colab运行时

#### 常见问题：
- **Google Drive未挂载**：重新执行Cell 1和Cell 2
- **依赖安装失败**：检查网络连接，重新执行Cell 3
- **模块未找到**：确保按顺序执行了所有Cell

### 6. 使用系统

执行完所有Cell后：
1. **界面启动**：系统会自动启动Gradio界面
2. **获取链接**：点击生成的公共链接访问界面
3. **开始游戏**：在聊天框中输入你的行动

### 7. 开发阶段切换

- **阶段1（JSON剧本）**：使用预设剧本，测试功能
- **阶段2（LLM模式）**：使用真实AI模型，需要先加载模型

## 📝 注意事项

1. **执行顺序很重要**：必须按序号顺序执行
2. **等待完成**：每个Cell执行完成后再执行下一个
3. **保存工作**：定期保存Colab笔记本
4. **资源限制**：注意Colab的GPU和内存限制

## 🎯 成功标志

当看到以下信息时，说明系统启动成功：
```
✨ 所有模块加载完成！
🎮 AI游戏主持人系统准备就绪
🌐 界面启动中...
```

然后会显示Gradio界面的公共链接，点击即可使用！
