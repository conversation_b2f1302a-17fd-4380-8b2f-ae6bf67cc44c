# 11_main_app.py
# 主应用启动 - 启动AI游戏主持人系统

import os
import sys
from datetime import datetime



def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查是否在Colab环境
    try:
        import google.colab
        print("✅ 运行在Google Colab环境")
        return True
    except ImportError:
        print("⚠️  不在Google Colab环境，某些功能可能受限")
        return False

def check_dependencies():
    """检查必要的依赖"""
    print("📦 检查依赖包...")
    
    required_packages = [
        'gradio',
        'torch',
        'transformers'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"⚠️  缺少依赖: {', '.join(missing_packages)}")
        print("💡 请运行 02_dependencies_enhanced.py 安装依赖")
        return False
    
    return True

def check_directories():
    """检查并创建必要的目录"""
    print("📁 检查目录结构...")
    
    try:
        # 检查AI_Game_Master目录
        if not os.path.exists(config.base_path):
            print(f"⚠️  AI_Game_Master目录不存在: {config.base_path}")
            print("🔧 正在创建目录...")
            os.makedirs(config.base_path, exist_ok=True)
        
        # 检查子目录
        for path_name, path_value in config.get_paths().items():
            if path_name != 'script_file' and not os.path.exists(path_value):
                print(f"📁 创建目录: {path_name}")
                os.makedirs(path_value, exist_ok=True)
        
        print("✅ 目录结构检查完成")
        return True
        
    except Exception as e:
        print(f"❌ 目录检查失败: {e}")
        return False

def print_startup_info():
    """打印启动信息"""
    startup_info = f"""
{'='*60}
🎮 AI游戏主持人系统启动成功！

📊 系统信息:
• 开发阶段: {config.development_stage}
• 使用模拟回复: {config.use_mock_responses}
• 基础路径: {config.base_path}
• 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🎯 第一阶段功能:
• ✅ JSON剧本系统
• ✅ 会话管理
• ✅ RAG文档系统
• ✅ 导出功能
• ✅ 聊天界面

🚀 第二阶段功能:
• ✅ LLM模型集成
• ✅ 模型管理界面
• ✅ 阶段切换功能

💡 使用说明:
1. 在聊天框中输入你的行动
2. 查看统计信息和游戏状态
3. 使用进度管理保存/加载游戏
4. 切换到第二阶段使用真实AI模型

🔗 界面将在下方启动...
{'='*60}
"""
    print(startup_info)

def create_and_launch_interface():
    """创建并启动界面"""
    print("🎨 创建用户界面...")
    
    try:
        interface = chat_interface.create_interface()
        
        print("🌐 启动Web界面...")
        
        # 启动界面
        interface.launch(
            share=True,
            server_name="0.0.0.0",
            server_port=7860,
            show_error=True,
            quiet=False
        )
        
    except Exception as e:
        print(f"❌ 界面启动失败: {e}")
        print("🔧 尝试简化启动...")
        try:
            interface.launch(share=True)
        except Exception as e2:
            print(f"❌ 简化启动也失败: {e2}")

def get_system_status():
    """获取系统状态"""
    status = {
        'timestamp': datetime.now().isoformat(),
        'config': {
            'development_stage': config.development_stage,
            'use_mock_responses': config.use_mock_responses,
            'base_path': config.base_path
        },
        'session': session_manager.get_session_stats(),
        'rag': rag_system.get_stats(),
        'script': script_manager.get_script_info()
    }
    return status

def switch_development_stage(stage: int):
    """切换开发阶段"""
    if stage in [1, 2]:
        config.set_development_stage(stage)
        print(f"🔄 已切换到开发阶段 {stage}")
        
        if stage == 1:
            print("📜 当前使用JSON剧本模拟AI回复")
        else:
            print("🤖 当前使用LLM模型生成回复")
            if model_manager:
                print("💡 使用 model_manager.load_model() 加载模型")
    else:
        print(f"❌ 无效的开发阶段: {stage}")

def main():
    """主函数"""
    print("🚀 启动AI游戏主持人系统...")
    print("=" * 60)

    # 检查必要模块是否已加载
    required_modules = [
        'config', 'session_manager', 'rag_system', 'script_manager',
        'model_manager', 'export_manager', 'chat_interface'
    ]

    missing_modules = []
    for module_name in required_modules:
        if module_name not in globals():
            missing_modules.append(module_name)

    if missing_modules:
        print(f"❌ 缺少必要模块: {', '.join(missing_modules)}")
        print("💡 请确保按顺序执行了前面的所有模块文件 (01-10)")
        print("📋 执行顺序:")
        print("   01_setup_and_mount.py")
        print("   02_dependencies_enhanced.py")
        print("   03_config.py")
        print("   04_data_models.py")
        print("   05_json_script_manager.py")
        print("   06_session_manager.py")
        print("   07_export_manager.py")
        print("   08_rag_document_system.py")
        print("   09_model_manager.py")
        print("   10-1_chat_interface_core.py")
        print("   10-2_chat_interface_session.py")
        print("   10-3_chat_interface_model.py")
        print("   10-4_chat_interface_ui.py")
        return

    print("✅ 所有必要模块已加载")

    # 环境检查
    is_colab = check_environment()

    # 依赖检查
    if not check_dependencies():
        print("❌ 依赖检查失败，请先安装必要的包")
        return

    # 目录检查
    if not check_directories():
        print("❌ 目录检查失败")
        return

    # 打印启动信息
    print_startup_info()

    # 创建并启动界面
    create_and_launch_interface()

# 如果直接运行此文件
if __name__ == "__main__":
    main()
else:
    # 如果作为模块导入，自动启动
    print("🎮 AI游戏主持人系统模块已加载")
    print("💡 使用 main() 函数启动系统")
    print("💡 使用 switch_development_stage(1或2) 切换开发阶段")
    print("💡 使用 get_system_status() 查看系统状态")

    # 自动启动
    main()

print("✨ 所有模块加载完成！")
print("🎮 AI游戏主持人系统准备就绪")
print("🌐 界面启动中...")
