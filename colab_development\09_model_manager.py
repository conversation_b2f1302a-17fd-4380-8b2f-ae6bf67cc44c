# 09_model_manager.py
# 模型管理器 - 第二阶段使用，加载和管理LLM模型

import os
import torch
from typing import Optional, Dict, Any, List
import gc
import json
from datetime import datetime

class ModelManager:
    """模型管理器 - 处理LLM模型的加载和推理"""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.device = self._get_device()
        self.model_loaded = False
        self.model_path = None
        self.model_config = None
        self.load_time = None
        
        print(f"🖥️  设备: {self.device}")
        if self.device == 'cuda':
            gpu_count = torch.cuda.device_count()
            print(f"🎮 GPU数量: {gpu_count}")
            for i in range(gpu_count):
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1e9
                print(f"🎮 GPU {i}: {torch.cuda.get_device_name(i)} - {gpu_memory:.1f}GB")
        
        # 检查模型路径
        self._check_model_availability()
    
    def _check_model_availability(self):
        """检查模型文件是否可用"""
        try:
            model_path = config.model_config['model_path']
            if os.path.exists(model_path):
                print(f"✅ 模型路径存在: {model_path}")
                
                # 检查模型文件
                model_files = []
                for root, dirs, files in os.walk(model_path):
                    for file in files:
                        if file.endswith(('.bin', '.safetensors', '.gguf', '.pt', '.pth')):
                            model_files.append(file)
                
                if model_files:
                    print(f"📁 找到 {len(model_files)} 个模型文件")
                    for file in model_files[:3]:  # 只显示前3个
                        print(f"   📄 {file}")
                    if len(model_files) > 3:
                        print(f"   ... 还有 {len(model_files) - 3} 个文件")
                else:
                    print(f"⚠️  模型目录中未找到模型文件")
            else:
                print(f"❌ 模型路径不存在: {model_path}")
                print(f"💡 请确保模型文件已上传到Google Drive")
        except Exception as e:
            print(f"⚠️  检查模型可用性时出错: {e}")
    
    def _get_device(self) -> str:
        """获取可用设备"""
        if torch.cuda.is_available():
            return 'cuda'
        else:
            return 'cpu'
    
    def check_gpu_memory(self) -> Dict[str, float]:
        """检查GPU内存使用情况"""
        if self.device == 'cuda':
            total = torch.cuda.get_device_properties(0).total_memory / 1e9
            allocated = torch.cuda.memory_allocated() / 1e9
            cached = torch.cuda.memory_reserved() / 1e9
            free = total - allocated
            
            return {
                'total': total,
                'allocated': allocated,
                'cached': cached,
                'free': free
            }
        else:
            return {'total': 0, 'allocated': 0, 'cached': 0, 'free': 0}
    
    def clear_gpu_memory(self):
        """清理GPU内存"""
        if self.device == 'cuda':
            torch.cuda.empty_cache()
            gc.collect()
            print("🧹 GPU内存已清理")
    
    def load_model(self, model_path: str = None, model_type: str = None) -> bool:
        """加载模型"""
        # 使用配置中的默认值
        if model_path is None:
            model_path = config.model_config['model_path']
        if model_type is None:
            model_type = config.model_config['model_type']
            
        print(f"📥 开始加载模型: {model_path}")
        print(f"🔧 模型类型: {model_type}")
        
        try:
            # 检查模型文件是否存在
            if not os.path.exists(model_path):
                print(f"❌ 模型路径不存在: {model_path}")
                print(f"💡 请确保模型文件已上传到: {model_path}")
                return False
            
            # 清理之前的模型
            if self.model_loaded:
                self.unload_model()
            
            # 记录加载开始时间
            start_time = datetime.now()
            
            # 根据模型类型加载
            if model_type.lower() == "llama":
                success = self._load_llama_model(model_path)
            else:
                print(f"❌ 不支持的模型类型: {model_type}")
                return False
            
            if success:
                self.model_loaded = True
                self.model_path = model_path
                self.load_time = datetime.now() - start_time
                
                print(f"✅ 模型加载成功: {os.path.basename(model_path)}")
                print(f"⏱️  加载耗时: {self.load_time.total_seconds():.1f}秒")
                
                # 显示内存使用情况
                memory_info = self.check_gpu_memory()
                if self.device == 'cuda':
                    print(f"🎮 GPU内存使用: {memory_info['allocated']:.1f}GB / {memory_info['total']:.1f}GB")
                    print(f"💾 可用内存: {memory_info['free']:.1f}GB")
                
                return True
            else:
                return False
                
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            print(f"🔍 错误类型: {type(e).__name__}")
            return False
    
    def _load_llama_model(self, model_path: str) -> bool:
        """加载Llama模型（支持量化）"""
        try:
            print("🔍 检测模型格式...")
            
            # 检查是否为GGUF格式（量化模型）
            gguf_files = [f for f in os.listdir(model_path) if f.endswith('.gguf')]
            
            if gguf_files:
                print(f"📦 检测到GGUF量化模型: {gguf_files[0]}")
                return self._load_gguf_model(model_path, gguf_files[0])
            else:
                print("📦 使用标准transformers加载")
                return self._load_transformers_model(model_path)
                
        except Exception as e:
            print(f"❌ Llama模型加载失败: {e}")
            return False
    
    def _load_gguf_model(self, model_path: str, gguf_file: str) -> bool:
        """加载GGUF量化模型"""
        try:
            # 使用llama-cpp-python加载GGUF模型
            try:
                from llama_cpp import Llama
                print("📚 使用llama-cpp-python加载GGUF模型...")
            except ImportError:
                print("❌ 需要安装llama-cpp-python: pip install llama-cpp-python")
                return False
            
            gguf_path = os.path.join(model_path, gguf_file)
            
            # 配置参数
            n_gpu_layers = -1 if self.device == 'cuda' else 0  # 全部GPU层
            n_ctx = 4096  # 上下文长度
            
            print(f"🤖 加载GGUF模型: {gguf_file}")
            print(f"🎮 GPU层数: {n_gpu_layers}")
            print(f"📝 上下文长度: {n_ctx}")
            
            self.model = Llama(
                model_path=gguf_path,
                n_gpu_layers=n_gpu_layers,
                n_ctx=n_ctx,
                verbose=False
            )
            
            # GGUF模型不需要单独的tokenizer
            self.tokenizer = None
            
            print("✅ GGUF模型加载成功")
            return True
            
        except Exception as e:
            print(f"❌ GGUF模型加载失败: {e}")
            return False
    
    def _load_transformers_model(self, model_path: str) -> bool:
        """使用transformers加载标准模型"""
        try:
            from transformers import AutoTokenizer, AutoModelForCausalLM
            import torch
            
            print("📚 加载tokenizer...")
            self.tokenizer = AutoTokenizer.from_pretrained(
                model_path,
                trust_remote_code=True,
                use_fast=True
            )
            
            # 设置pad_token
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            print("🤖 加载模型...")
            
            # 配置加载参数
            load_config = {
                'torch_dtype': torch.float16 if self.device == 'cuda' else torch.float32,
                'trust_remote_code': True,
                'low_cpu_mem_usage': True,
            }
            
            # GPU配置
            if self.device == 'cuda':
                load_config['device_map'] = "auto"
                # 如果有多个GPU，使用所有GPU
                if torch.cuda.device_count() > 1:
                    print(f"🎮 使用多GPU加载 ({torch.cuda.device_count()}个GPU)")
            
            self.model = AutoModelForCausalLM.from_pretrained(
                model_path,
                **load_config
            )
            
            print("✅ Transformers模型加载成功")
            return True
            
        except Exception as e:
            print(f"❌ Transformers模型加载失败: {e}")
            return False
    
    def unload_model(self):
        """卸载模型"""
        if self.model_loaded:
            print("🗑️  卸载模型...")
            
            self.model = None
            self.tokenizer = None
            self.model_loaded = False
            self.model_path = None
            
            # 清理内存
            self.clear_gpu_memory()
            
            print("✅ 模型已卸载")
    
    def generate_response(self, prompt: str, max_tokens: int = 512, 
                         temperature: float = 0.7, top_p: float = 0.9) -> str:
        """生成回复"""
        if not self.model_loaded:
            return "❌ 模型未加载，请先加载模型"
        
        try:
            # 构建游戏主持人提示词
            system_prompt = self._build_system_prompt()
            full_prompt = f"{system_prompt}\n\n玩家: {prompt}\n\nAI游戏主持人:"
            
            # 根据模型类型生成回复
            if hasattr(self.model, '__call__'):  # GGUF模型
                return self._generate_gguf_response(full_prompt, max_tokens, temperature, top_p)
            else:  # Transformers模型
                return self._generate_transformers_response(full_prompt, max_tokens, temperature, top_p)
            
        except Exception as e:
            print(f"❌ 生成回复失败: {e}")
            return f"❌ 生成回复时出错: {str(e)}"

    def _generate_gguf_response(self, prompt: str, max_tokens: int,
                               temperature: float, top_p: float) -> str:
        """使用GGUF模型生成回复"""
        try:
            response = self.model(
                prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=top_p,
                stop=["玩家:", "\n玩家:", "用户:", "\n用户:"],
                echo=False
            )

            ai_response = response['choices'][0]['text'].strip()
            return ai_response

        except Exception as e:
            print(f"❌ GGUF模型生成失败: {e}")
            return f"❌ GGUF生成错误: {str(e)}"

    def _generate_transformers_response(self, prompt: str, max_tokens: int,
                                      temperature: float, top_p: float) -> str:
        """使用Transformers模型生成回复"""
        try:
            # 编码输入
            inputs = self.tokenizer.encode(prompt, return_tensors="pt")
            if self.device == 'cuda':
                inputs = inputs.to(self.device)

            # 生成回复
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_new_tokens=max_tokens,
                    temperature=temperature,
                    top_p=top_p,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id
                )

            # 解码输出
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)

            # 提取AI回复部分
            if "AI游戏主持人:" in response:
                ai_response = response.split("AI游戏主持人:")[-1].strip()
            else:
                ai_response = response[len(prompt):].strip()

            return ai_response

        except Exception as e:
            print(f"❌ Transformers模型生成失败: {e}")
            return f"❌ Transformers生成错误: {str(e)}"

    def _build_system_prompt(self) -> str:
        """构建系统提示词"""
        # 尝试获取RAG上下文，如果不可用则使用默认
        try:
            rag_context = rag_system.get_context_summary()
        except:
            rag_context = "游戏刚开始，玩家准备开始新的冒险。"

        system_prompt = f"""你是一个专业的AI游戏主持人，正在主持一场Elder Scrolls风格的RPG游戏。

游戏设定:
- 世界观: Elder Scrolls宇宙
- 风格: 奇幻RPG，包含战斗、探索、对话
- 语言: 中文
- 语调: 沉浸式、描述性、引人入胜

当前游戏状态:
{rag_context}

你的职责:
1. 根据玩家的行动描述游戏世界的反应
2. 维护游戏的连贯性和逻辑性
3. 提供有趣的选择和挑战
4. 使用生动的描述和适当的表情符号
5. 保持Elder Scrolls的世界观和氛围

回复格式:
- 使用表情符号增强沉浸感
- 描述环境、NPC反应、战斗结果等
- 在适当时提供选择选项
- 保持回复长度适中（100-300字）"""

        return system_prompt

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        info = {
            'model_loaded': self.model_loaded,
            'model_path': self.model_path,
            'device': self.device,
            'memory_info': self.check_gpu_memory()
        }

        if self.model_loaded and hasattr(self.model, 'config'):
            try:
                info['model_config'] = {
                    'model_type': getattr(self.model.config, 'model_type', 'unknown'),
                    'vocab_size': getattr(self.model.config, 'vocab_size', 'unknown'),
                    'hidden_size': getattr(self.model.config, 'hidden_size', 'unknown'),
                    'num_layers': getattr(self.model.config, 'num_hidden_layers', 'unknown')
                }
            except:
                info['model_config'] = 'unavailable'

        return info

    def test_model(self) -> str:
        """测试模型"""
        if not self.model_loaded:
            return "❌ 模型未加载"

        test_prompt = "你好，我是一名冒险者。"
        response = self.generate_response(test_prompt, max_tokens=100)

        return f"测试提示: {test_prompt}\n\nAI回复: {response}"

# 创建全局模型管理器实例（第二阶段使用）
# 注意：只有在第二阶段才会实际使用
if config.is_development_stage_2():
    model_manager = ModelManager()
    print("🤖 模型管理器加载完成（第二阶段）")
    print("📋 使用 model_manager.load_model() 加载模型")
else:
    model_manager = None
    print("📜 第一阶段：跳过模型管理器加载")

print("🎮 准备加载聊天界面...")
