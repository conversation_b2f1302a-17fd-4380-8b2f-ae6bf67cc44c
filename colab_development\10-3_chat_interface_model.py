# 10-3_chat_interface_model.py
# 聊天界面模型管理功能 - 模型加载、卸载、状态管理

# 检查依赖模块是否已加载
def check_model_dependencies():
    """检查模型管理所需的依赖模块"""
    required_modules = ['config', 'model_manager']
    missing_modules = []

    for module_name in required_modules:
        if module_name not in globals():
            missing_modules.append(module_name)

    if missing_modules:
        print(f"⚠️  模型管理模块缺少依赖: {', '.join(missing_modules)}")
        return False

    return True

# 检查依赖
if not check_model_dependencies():
    print("❌ 模型管理依赖检查失败")
else:
    print("✅ 模型管理依赖检查通过")

class ChatInterfaceModel:
    """聊天界面模型管理功能"""
    
    def load_model(self) -> str:
        """加载模型"""
        try:
            # 检查是否为第二阶段
            if not config.is_development_stage_2():
                return "⚠️  当前为第一阶段，请先切换到第二阶段"

            if not model_manager:
                return "❌ 模型管理器未初始化"

            # 加载模型
            success = model_manager.load_model()

            if success:
                model_info = model_manager.get_model_info()
                memory_info = model_info['memory_info']

                result = f"✅ 模型加载成功!\n"
                result += f"📁 模型路径: {config.model_config['model_name']}\n"
                result += f"🖥️  设备: {model_info['device']}\n"

                if model_info['device'] == 'cuda':
                    result += f"🎮 GPU内存: {memory_info['allocated']:.1f}GB / {memory_info['total']:.1f}GB\n"
                    result += f"💾 可用内存: {memory_info['free']:.1f}GB"

                return result
            else:
                return "❌ 模型加载失败，请检查模型文件是否存在"

        except Exception as e:
            return f"❌ 加载模型时出错: {str(e)}"

    def unload_model(self) -> str:
        """卸载模型"""
        try:
            if not model_manager:
                return "❌ 模型管理器未初始化"

            if not model_manager.model_loaded:
                return "📝 当前没有加载的模型"

            model_manager.unload_model()
            return "✅ 模型已卸载，GPU内存已释放"

        except Exception as e:
            return f"❌ 卸载模型时出错: {str(e)}"

    def get_model_status(self) -> str:
        """获取模型状态"""
        try:
            if not model_manager:
                return "❌ 模型管理器未初始化（当前为第一阶段）"

            model_info = model_manager.get_model_info()

            if model_info['model_loaded']:
                memory_info = model_info['memory_info']
                status = f"✅ **模型已加载**\n"
                status += f"📁 模型: {config.model_config['model_name']}\n"
                status += f"🖥️  设备: {model_info['device']}\n"

                if model_info['device'] == 'cuda':
                    status += f"🎮 GPU内存: {memory_info['allocated']:.1f}GB / {memory_info['total']:.1f}GB\n"
                    status += f"💾 可用内存: {memory_info['free']:.1f}GB\n"

                if hasattr(model_manager, 'load_time') and model_manager.load_time:
                    status += f"⏱️  加载耗时: {model_manager.load_time.total_seconds():.1f}秒"

                return status
            else:
                return f"📝 **模型未加载**\n💡 点击'加载模型'开始使用AI功能"

        except Exception as e:
            return f"❌ 获取模型状态失败: {str(e)}"

    def switch_to_stage_2(self) -> str:
        """切换到第二阶段"""
        try:
            config.set_development_stage(2)
            return "✅ 已切换到第二阶段（LLM模式）\n💡 现在可以加载模型使用AI功能"
        except Exception as e:
            return f"❌ 切换阶段失败: {str(e)}"

    def switch_to_stage_1(self) -> str:
        """切换到第一阶段"""
        try:
            config.set_development_stage(1)
            return "✅ 已切换到第一阶段（JSON剧本模式）\n💡 现在使用JSON剧本模拟AI回复"
        except Exception as e:
            return f"❌ 切换阶段失败: {str(e)}"

    def test_model(self) -> str:
        """测试模型"""
        try:
            if not model_manager:
                return "❌ 模型管理器未初始化"
            
            if not model_manager.model_loaded:
                return "❌ 模型未加载，请先加载模型"
            
            test_prompt = "你好，我是一名冒险者。"
            response = model_manager.generate_response(test_prompt, max_tokens=100)
            
            return f"🧪 **模型测试**\n\n**输入**: {test_prompt}\n\n**输出**: {response}"
            
        except Exception as e:
            return f"❌ 模型测试失败: {str(e)}"

    def get_model_info_detailed(self) -> str:
        """获取详细的模型信息"""
        try:
            if not model_manager:
                return "❌ 模型管理器未初始化"
            
            model_info = model_manager.get_model_info()
            
            info_text = f"""
🤖 **模型详细信息**

📊 **基本状态**
• 模型已加载: {'✅ 是' if model_info['model_loaded'] else '❌ 否'}
• 设备: {model_info['device']}
• 模型路径: {model_info.get('model_path', '未设置')}

💾 **内存信息**
• 总内存: {model_info['memory_info']['total']:.1f}GB
• 已分配: {model_info['memory_info']['allocated']:.1f}GB
• 已缓存: {model_info['memory_info']['cached']:.1f}GB
• 可用内存: {model_info['memory_info']['free']:.1f}GB

⚙️ **配置信息**
• 模型名称: {config.model_config['model_name']}
• 模型类型: {config.model_config['model_type']}
• 量化类型: {config.model_config['quantization']}
• 最大tokens: {config.model_config['max_tokens']}
• 温度: {config.model_config['temperature']}
• Top-p: {config.model_config['top_p']}
            """
            
            if model_info['model_loaded'] and hasattr(model_manager, 'load_time') and model_manager.load_time:
                info_text += f"\n⏱️  **性能信息**\n• 加载耗时: {model_manager.load_time.total_seconds():.1f}秒"
            
            return info_text.strip()
            
        except Exception as e:
            return f"❌ 获取模型详细信息失败: {str(e)}"

# 创建模型管理功能实例
chat_model = ChatInterfaceModel()

print("🤖 聊天界面模型管理功能加载完成")
print("🎨 准备加载界面创建功能...")
