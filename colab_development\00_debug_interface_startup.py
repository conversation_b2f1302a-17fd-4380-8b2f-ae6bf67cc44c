# 00_debug_interface_startup.py
# 界面启动问题诊断脚本

import os
import sys
from datetime import datetime

def check_module_availability():
    """检查所有必要模块是否可用"""
    print("🔍 检查模块可用性...")
    
    # 检查基础模块
    required_modules = [
        'config', 'session_manager', 'rag_system', 'script_manager', 
        'model_manager', 'export_manager', 'RAGDocument'
    ]
    
    # 检查聊天界面模块
    chat_modules = [
        'chat_core', 'chat_session', 'chat_model', 'chat_ui', 'chat_interface'
    ]
    
    all_modules = required_modules + chat_modules
    available_modules = []
    missing_modules = []
    
    for module_name in all_modules:
        try:
            module_obj = globals().get(module_name)
            if module_obj is not None:
                available_modules.append(module_name)
                print(f"✅ {module_name}: 可用")
            else:
                missing_modules.append(module_name)
                print(f"❌ {module_name}: 缺失")
        except Exception as e:
            missing_modules.append(f"{module_name}({e})")
            print(f"❌ {module_name}: 错误 - {e}")
    
    print(f"\n📊 模块统计:")
    print(f"✅ 可用模块: {len(available_modules)}/{len(all_modules)}")
    print(f"❌ 缺失模块: {len(missing_modules)}")
    
    return available_modules, missing_modules

def check_chat_interface_creation():
    """测试聊天界面创建"""
    print("\n🎨 测试聊天界面创建...")
    
    try:
        # 检查chat_interface是否存在
        if 'chat_interface' not in globals():
            print("❌ chat_interface 未在全局作用域中定义")
            return False
        
        chat_interface_obj = globals().get('chat_interface')
        if chat_interface_obj is None:
            print("❌ chat_interface 为 None")
            return False
        
        print("✅ chat_interface 对象存在")
        
        # 检查方法
        if not hasattr(chat_interface_obj, 'create_interface'):
            print("❌ chat_interface 缺少 create_interface 方法")
            return False
        
        print("✅ create_interface 方法存在")
        
        # 尝试创建界面
        print("🔧 尝试创建界面...")
        interface = chat_interface_obj.create_interface()
        
        if interface is None:
            print("❌ 界面创建返回 None")
            return False
        
        print("✅ 界面创建成功")
        return True
        
    except Exception as e:
        print(f"❌ 界面创建失败: {e}")
        print(f"❌ 错误类型: {type(e).__name__}")
        import traceback
        print(f"❌ 详细错误:\n{traceback.format_exc()}")
        return False

def check_dependencies():
    """检查依赖包"""
    print("\n📦 检查依赖包...")
    
    required_packages = [
        'gradio',
        'torch',
        'transformers',
        'json',
        'os',
        'datetime'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖: {', '.join(missing_packages)}")
        return False
    
    return True

def check_environment():
    """检查运行环境"""
    print("\n🌍 检查运行环境...")
    
    # 检查是否在Colab
    try:
        import google.colab
        print("✅ 运行在Google Colab环境")
        is_colab = True
    except ImportError:
        print("⚠️  不在Google Colab环境")
        is_colab = False
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"🐍 Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查工作目录
    cwd = os.getcwd()
    print(f"📁 当前工作目录: {cwd}")
    
    return is_colab

def create_simple_interface():
    """创建一个简单的测试界面"""
    print("\n🧪 创建简单测试界面...")
    
    try:
        import gradio as gr
        
        def simple_chat(message, history):
            response = f"测试回复: {message}"
            history.append((message, response))
            return "", history
        
        with gr.Blocks(title="AI游戏主持人 - 诊断测试") as demo:
            gr.Markdown("# 🧪 AI游戏主持人 - 诊断测试界面")
            gr.Markdown("如果您看到这个界面，说明基础功能正常")
            
            chatbot = gr.Chatbot(label="测试聊天", height=300)
            
            with gr.Row():
                msg = gr.Textbox(placeholder="输入测试消息...", scale=4)
                send = gr.Button("发送", scale=1)
            
            msg.submit(simple_chat, [msg, chatbot], [msg, chatbot])
            send.click(simple_chat, [msg, chatbot], [msg, chatbot])
        
        print("✅ 简单界面创建成功")
        return demo
        
    except Exception as e:
        print(f"❌ 简单界面创建失败: {e}")
        return None

def main():
    """主诊断函数"""
    print("🔧 AI游戏主持人界面启动诊断")
    print("=" * 50)
    print(f"⏰ 诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 检查环境
    is_colab = check_environment()
    
    # 2. 检查依赖包
    deps_ok = check_dependencies()
    
    # 3. 检查模块
    available_modules, missing_modules = check_module_availability()
    
    # 4. 检查聊天界面
    interface_ok = check_chat_interface_creation()
    
    # 5. 总结
    print("\n" + "=" * 50)
    print("📋 诊断总结:")
    print(f"🌍 Colab环境: {'✅' if is_colab else '❌'}")
    print(f"📦 依赖包: {'✅' if deps_ok else '❌'}")
    print(f"📚 模块加载: {len(available_modules)}/{len(available_modules) + len(missing_modules)}")
    print(f"🎨 界面创建: {'✅' if interface_ok else '❌'}")
    
    # 6. 建议
    print("\n💡 建议:")
    if not deps_ok:
        print("1. 运行 02_dependencies_enhanced.py 安装依赖")
    
    if missing_modules:
        print("2. 按顺序重新执行以下模块:")
        for i, module in enumerate(['01_setup_and_mount.py', '02_dependencies_enhanced.py', 
                                   '03_config.py', '04_data_models.py', '05_json_script_manager.py',
                                   '06_session_manager.py', '07_export_manager.py', '08_rag_document_system.py',
                                   '09_model_manager.py', '10-1_chat_interface_core.py',
                                   '10-2_chat_interface_session.py', '10-3_chat_interface_model.py',
                                   '10-4_chat_interface_ui.py'], 1):
            print(f"   {i:2d}. {module}")
    
    if not interface_ok:
        print("3. 如果问题持续，尝试重启Colab运行时")
        print("4. 可以尝试运行简单测试界面")
        
        # 尝试创建简单界面
        simple_demo = create_simple_interface()
        if simple_demo:
            print("\n🚀 启动简单测试界面...")
            try:
                simple_demo.launch(share=True, quiet=False)
            except Exception as e:
                print(f"❌ 简单界面启动失败: {e}")

if __name__ == "__main__":
    main()
else:
    print("🔧 诊断脚本已加载")
    print("💡 使用 main() 函数开始诊断")
