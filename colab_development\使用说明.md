# AI游戏主持人 - Colab开发版使用说明

## 概述
这是AI游戏主持人项目的新开发版本，采用模块化.py文件架构，专为Google Colab环境设计。

## 重要提醒
⚠️ **这些文件不要在本地运行！** 它们专为Google Colab设计，每个.py文件对应Colab中的一个cell。

## 开发阶段
### 第一阶段：功能测试（当前）
- 使用JSON剧本模拟AI回复
- 测试所有界面和功能
- 验证导出、RAG文档等功能

### 第二阶段：LLM集成
- 加载真实的大语言模型
- 实现智能对话生成
- 性能优化

## 使用步骤

### 1. 在Google Colab中创建新笔记本
访问 [Google Colab](https://colab.research.google.com/) 并创建新的笔记本。

### 2. 按顺序复制文件内容
将以下文件的内容按顺序复制到Colab的cell中：

```
01_setup_and_mount.py        # 环境设置和Google Drive挂载
02_dependencies.py           # 依赖安装
03_config.py                 # 配置管理
04_data_models.py            # 数据结构定义
05_json_script_manager.py    # JSON剧本管理器
06_session_manager.py        # 会话管理
07_export_manager.py         # 导出功能
08_rag_document_system.py    # RAG文档系统
09_chat_interface.py         # 聊天界面
10_main_app.py               # 主应用启动
```

### 3. 逐个执行cell
按顺序执行每个cell，观察输出信息，确保没有错误。

### 4. 上传剧本文件（可选）
如果要使用自定义剧本，将JSON文件上传到Google Drive的 `/AI_Game_Master/scripts/` 目录。

## 功能特性

### ✅ 已实现功能
- **原生Gradio界面**：简洁清爽的聊天界面
- **JSON剧本系统**：第一阶段使用预设剧本模拟AI回复
- **会话管理**：自动保存对话历史，支持会话恢复
- **RAG文档系统**：自动追踪游戏状态、物品、事件、玩家偏好
- **多格式导出**：
  - JSON客户格式（简单对话格式）
  - JSON完整格式（包含元数据）
  - DOCX文档（格式化的对话记录）
  - 游戏总结（包含物品、事件、偏好的综合报告）
- **实时统计**：显示会话信息和游戏状态
- **自动端口选择**：避免端口冲突问题

### 🎮 游戏特性
- **Elder Scrolls世界观**：基于上古卷轴系列的奇幻RPG设定
- **智能上下文**：根据用户输入提供相关回复
- **战斗系统**：包含生命值、技能、物品等RPG元素
- **探索机制**：支持多种选择和分支剧情

## 配置说明

### 开发阶段切换
在 `03_config.py` 中可以切换开发阶段：
```python
config.set_development_stage(1)  # 第一阶段：JSON剧本
config.set_development_stage(2)  # 第二阶段：LLM集成
```

### 路径配置
所有路径都使用Colab格式：
```python
base_path = '/content/drive/MyDrive/AI_Game_Master'
```

### 界面配置
可以在 `03_config.py` 中修改界面设置：
```python
ui_config = {
    'title': '🎮 AI游戏主持人',
    'theme': 'dark',
    'share': True,  # 允许外部访问
}
```

## 文件结构
```
/content/drive/MyDrive/AI_Game_Master/
├── conversations/          # 对话记录
├── exports/               # 导出文件
├── rag_docs/             # RAG文档
└── scripts/              # 剧本文件
    └── Ursus_Werebear_Awakens.json
```

## 故障排除

### 常见问题
1. **Google Drive未挂载**
   - 确保执行了 `01_setup_and_mount.py`
   - 按提示授权Google Drive访问

2. **依赖安装失败**
   - 重新执行 `02_dependencies.py`
   - 检查网络连接

3. **界面无法启动**
   - 检查所有前置cell是否正确执行
   - 查看错误信息并重新执行相关cell

4. **剧本文件不存在**
   - 系统会自动创建示例剧本
   - 也可以手动上传自定义剧本文件

### 调试技巧
- 查看每个cell的输出信息
- 使用 `config.get_paths()` 检查路径配置
- 使用 `session_manager.get_session_stats()` 查看会话状态

## 第二阶段准备
当准备集成LLM时：
1. 切换到开发阶段2
2. 执行 `11_model_manager.py`
3. 加载合适的模型文件
4. 测试模型推理功能

## 技术支持
如果遇到问题：
1. 检查Google Colab环境是否正常
2. 确认Google Drive挂载成功
3. 查看控制台错误信息
4. 重新执行相关的cell

## 更新日志
- **v1.0**: 完整的第一阶段功能实现
- 模块化架构设计
- JSON剧本系统
- 完整的导出功能
- RAG文档自动管理

---

🎮 **开始你的Elder Scrolls冒险吧！**
