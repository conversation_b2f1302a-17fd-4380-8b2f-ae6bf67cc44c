# test_dependencies.py
# 测试依赖安装功能

import sys
import subprocess

def test_install_with_progress():
    """测试带进度的安装功能"""
    print("🧪 测试依赖安装功能...")
    
    # 导入我们的安装函数
    sys.path.append('.')
    
    try:
        # 导入依赖安装模块
        exec(open('02_dependencies_enhanced.py').read())
        
        print("✅ 依赖安装模块加载成功")
        print("🎯 可以在Colab中使用以下命令测试:")
        print("   exec(open('02_dependencies_enhanced.py').read())")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_install_with_progress()
