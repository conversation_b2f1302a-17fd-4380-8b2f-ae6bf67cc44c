# 03_config.py
# 项目配置管理

import os
from typing import Dict, Any

class Config:
    """AI游戏主持人配置类"""
    
    def __init__(self):
        # 基础路径配置 - 自动检测环境
        self.base_path = self._get_base_path()
        self.conversations_path = os.path.join(self.base_path, 'game_saves')  # 游戏进度保存目录
        self.exports_path = os.path.join(self.base_path, 'exports')
        self.rag_docs_path = os.path.join(self.base_path, 'rag_docs')
        self.scripts_path = os.path.join(self.base_path, 'scripts')
        self.models_path = os.path.join(self.base_path, 'models')  # 模型文件目录

        # 确保所有必要目录存在
        self._ensure_directories_exist()
        
        # 开发阶段配置
        self.development_stage = 2  # 1: JSON剧本测试, 2: LLM集成
        self.use_mock_responses = True  # 第一阶段使用模拟回复
        
        # 剧本文件配置
        self.script_file = os.path.join(self.scripts_path, 'Ursus_Werebear_Awakens.json')
        
        # 界面配置
        self.ui_config = {
            'title': '🎮 AI游戏主持人',
            'description': 'Elder Scrolls风格的RPG游戏体验',
            'theme': 'dark',
            'font_family': 'Abezee, sans-serif',
            'primary_color': '#107c10',
            'background_color': '#000000',
            'share': True,  # 允许外部访问
        }
        
        # 会话配置
        self.session_config = {
            'max_history_length': 1000,  # 最大历史记录长度
            'auto_save_interval': 10,    # 自动保存间隔（消息数）
            'session_timeout': 3600,     # 会话超时时间（秒）
        }
        
        # 导出配置
        self.export_config = {
            'json_format': 'conversation',  # 导出JSON格式
            'docx_template': 'default',     # DOCX模板
            'include_metadata': True,       # 包含元数据
            'compress_exports': False,      # 压缩导出文件
        }
        
        # RAG文档配置
        self.rag_config = {
            'update_frequency': 5,  # 每5条消息更新一次
            'max_events': 50,       # 最大事件记录数
            'max_inventory': 100,   # 最大物品记录数
        }
        
        # 模型配置（第二阶段使用）
        self.model_config = {
            'model_name': 'Llama-3.1-70B-Instruct-lorablated-Q3_K_XL',
            'model_path': os.path.join(self.models_path, 'Llama-3.1-70B-Instruct-lorablated-Q3_K_XL'),
            'model_type': 'llama',
            'max_tokens': 512,
            'temperature': 0.7,
            'top_p': 0.9,
            'device': 'cuda' if self._check_gpu() else 'cpu',
            'quantization': 'Q3_K_XL',  # 量化类型
            'use_flash_attention': True,  # 使用Flash Attention优化
            'max_memory_gb': 24,  # 最大内存使用限制
        }

    def _get_base_path(self) -> str:
        """自动检测环境并返回正确的基础路径"""
        # 检测Google Drive挂载路径
        possible_paths = [
            '/content/drive/MyDrive/AI_Game_Master',  # Colab标准路径
            'G:/我的云端硬盘/AI_Game_Master',          # 本地挂载路径
            '/content/drive/My Drive/AI_Game_Master'   # 备用路径
        ]

        for path in possible_paths:
            if os.path.exists(os.path.dirname(path)):
                print(f"🔍 检测到环境路径: {path}")
                return path

        # 如果都不存在，使用默认Colab路径
        default_path = '/content/drive/MyDrive/AI_Game_Master'
        print(f"⚠️  使用默认路径: {default_path}")
        return default_path

    def _ensure_directories_exist(self):
        """确保所有必要的目录存在"""
        directories = [
            ('游戏存档', self.conversations_path),
            ('导出文件', self.exports_path),
            ('RAG文档', self.rag_docs_path),
            ('剧本文件', self.scripts_path),
            ('模型文件', self.models_path)
        ]

        print(f"📂 基础路径: {self.base_path}")

        # 首先确保基础目录存在
        try:
            os.makedirs(self.base_path, exist_ok=True)
            print(f"✅ 基础目录已确保存在")
        except Exception as e:
            print(f"❌ 创建基础目录失败: {e}")
            return

        # 创建子目录
        for name, directory in directories:
            try:
                os.makedirs(directory, exist_ok=True)
                print(f"📁 {name}目录已确保存在: {directory}")

                # 检查写入权限
                if os.access(directory, os.W_OK):
                    print(f"✅ {name}目录权限正常")
                else:
                    print(f"⚠️  {name}目录可能没有写入权限")

            except Exception as e:
                print(f"❌ 创建{name}目录失败 {directory}: {e}")

    def _check_gpu(self) -> bool:
        """检查GPU可用性"""
        try:
            import torch
            return torch.cuda.is_available()
        except ImportError:
            return False
    
    def get_paths(self) -> Dict[str, str]:
        """获取所有路径配置"""
        return {
            'base': self.base_path,
            'game_saves': self.conversations_path,  # 游戏进度保存目录
            'exports': self.exports_path,
            'rag_docs': self.rag_docs_path,
            'scripts': self.scripts_path,
            'models': self.models_path,  # 模型文件目录
            'script_file': self.script_file,
            'model_path': self.model_config['model_path'],  # 当前模型路径
        }
    
    def is_development_stage_1(self) -> bool:
        """检查是否为第一开发阶段"""
        return self.development_stage == 1
    
    def is_development_stage_2(self) -> bool:
        """检查是否为第二开发阶段"""
        return self.development_stage == 2
    
    def set_development_stage(self, stage: int):
        """设置开发阶段"""
        if stage in [1, 2]:
            self.development_stage = stage
            self.use_mock_responses = (stage == 1)
            print(f"🔄 切换到开发阶段 {stage}")
        else:
            print("❌ 无效的开发阶段，只支持1或2")

# 创建全局配置实例
config = Config()

print("⚙️  配置加载完成")
print(f"📂 基础路径: {config.base_path}")
print(f"💾 游戏存档路径: {config.conversations_path}")
print(f"🎯 开发阶段: {config.development_stage}")
print(f"🎮 使用模拟回复: {config.use_mock_responses}")
print(f"🖥️  GPU可用: {config._check_gpu()}")
print("✨ 准备加载数据模型...")
